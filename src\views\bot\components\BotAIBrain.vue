<script setup lang="ts">
import { $t } from "@/plugins/i18n";

interface Props {
  systemPrompt?: string;
  loading?: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:systemPrompt", value: string): void;
  (e: "generatePrompt"): void;
}>();

const handlePromptUpdate = (value: string) => {
  emit("update:systemPrompt", value);
};

const handleGeneratePrompt = () => {
  emit("generatePrompt");
};
</script>

<template>
  <div class="card">
    <div class="flex flex-row items-center justify-between">
      <h2 class="section-title">{{ $t("AI Brain") }}</h2>
      <el-button
        class="gemini-button"
        :loading="props.loading"
        round
        @click="handleGeneratePrompt"
      >
        {{ $t("✨ Prompt Generator Assistant") }}
      </el-button>
    </div>
    <el-form-item :label="$t('Suggestion - Prompt')" prop="systemPrompt">
      <el-input
        :model-value="props.systemPrompt"
        type="textarea"
        :rows="8"
        :placeholder="$t('This is the most important part...')"
        @update:model-value="handlePromptUpdate"
      />
    </el-form-item>
  </div>
</template>

<style lang="scss" scoped>
.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.gemini-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}
</style>
