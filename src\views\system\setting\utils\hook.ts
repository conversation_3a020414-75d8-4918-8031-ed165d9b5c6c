import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, RoleFilterProps } from "./type";
import {
  getRoles,
  getRolesDropdown,
  createRole,
  updateRoleById,
  bulkDeleteRoles,
  deleteRole,
  deleteRolePermanent,
  bulkDeleteRolesPermanent,
  restoreRole,
  bulkRestoreRoles
} from "./auth-api";

export function useRoleHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<RoleFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const rolesDropdown = ref([]);

  // Form refs
  const roleFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "active"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetRoles = async () => {
    try {
      loading.value = true;
      const res = await getRoles(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching roles:", error);
      message($t("Failed to fetch roles"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnGetRolesDropdown = async () => {
    try {
      const res = await getRolesDropdown();
      rolesDropdown.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching roles dropdown:", error);
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = (val: number) => {
    pagination.currentPage = val;
    fnGetRoles();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    fnGetRoles();
  };

  const fnHandleSortChange = (sortBy: string, sortOrder: string) => {
    sort.value = { sortBy, sortOrder };
    fnGetRoles();
  };

  const fnHandleCreateRole = async (data: any) => {
    try {
      loading.value = true;
      const response = await createRole(data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetRoles();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateRole = async (id: number, data: any) => {
    try {
      loading.value = true;
      const response = await updateRoleById(id, data);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetRoles();
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteRole(row.id);
      message($t("Deleted successfully"), { type: "success" });
      fnGetRoles();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting role:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteRoles({ ids: selectedIds });
      message($t("Deleted successfully"), { type: "success" });
      fnGetRoles();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting roles:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteRolePermanent(row.id);
      message($t("Permanently deleted successfully"), { type: "success" });
      fnGetRoles();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting role:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteRolesPermanent({ ids: selectedIds });
      message($t("Permanently deleted successfully"), { type: "success" });
      fnGetRoles();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting roles:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await restoreRole(row.id);
      message($t("Restored successfully"), { type: "success" });
      fnGetRoles();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error restoring role:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  const handleBulkRestore = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await bulkRestoreRoles({ ids: selectedIds });
      message($t("Restored successfully"), { type: "success" });
      fnGetRoles();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk restoring roles:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };
  /*
   ***************************
   *   Form handlers and actions
   ***************************
   */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...row };
    drawerVisible.value = true;
  };

  const handleFilter = async (values: RoleFilterProps) => {
    filterRef.value = values;
    await fnGetRoles();
  };

  const handleSubmit = async (values: FieldValues) => {
    let success = false;
    if (values.id != null) {
      success = await fnHandleUpdateRole(Number(values.id), values);
    } else {
      success = await fnHandleCreateRole(values);
      if (success) {
        drawerValues.value = { status: "active" };
        roleFormRef.value?.resetForm();
      }
    }
  };

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */

  onMounted(() => {
    fnGetRoles();
  });

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    rolesDropdown,
    handleBulkDelete,
    handleDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,
    fnGetRoles,
    fnGetRolesDropdown,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    roleFormRef,
    handleSubmit,
    handleFilter,
    handleEdit
  };
}
