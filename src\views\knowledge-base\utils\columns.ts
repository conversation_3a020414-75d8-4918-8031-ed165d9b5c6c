import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag, ElButton, ElTooltip } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";
import { IconifyIconOnline } from "@/components/ReIcon";
import { getFileIcon, getFileIconColor } from "@/views/knowledge-base/utils/type";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Knowledge Base Name")
  },
  {
    prop: "type",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => {
      const typeColors = {
        file: "primary",
        text: "success",
        url: "warning",
        document: "info"
      };
      return h(
        ElTag,
        {
          type: typeColors[row.type] || "info",
          size: "small"
        },
        () => capitalized(row.type || "")
      );
    }
  },
  {
    prop: "status",
    align: "left",
    width: 130,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusConfig = {
        ready: {
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600",
          text: $t("Ready")
        },
        pending: {
          class: "bg-gray-100 text-gray-800",
          icon: "ri:pause-circle-line",
          iconClass: "text-gray-600",
          text: $t("Pending")
        },
        processing: {
          class: "bg-blue-100 text-blue-800",
          icon: "ri:loader-4-line",
          iconClass: "text-blue-600 animate-spin",
          text: $t("Processing")
        },
        error: {
          class: "bg-red-100 text-red-800",
          icon: "ri:error-warning-fill",
          iconClass: "text-red-600",
          text: $t("Failed")
        }
      };

      const config = statusConfig[row.status] || statusConfig.pending;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    prop: "storagePath",
    align: "center",
    sortable: false,
    width: 120,
    headerRenderer: () => $t("Attachment"),
    cellRenderer: ({ row }) => {
      if (!row.storagePath) return "-";

      const getFileExtension = (path: string) => {
        return path.split(".").pop()?.toLowerCase() || "";
      };

      const getFileName = (path: string) => {
        return path.split("/").pop() || path;
      };

      const extension = getFileExtension(row.storagePath);
      const icon = getFileIcon(extension);
      const color = getFileIconColor(extension);
      const fileName = getFileName(row.storagePath);

      return h(
        ElTooltip,
        {
          content: fileName,
          placement: "top"
        },
        {
          default: () =>
            h(
              "a",
              {
                href: row.url,
                download: row.name || "document",
                target: "_blank",
                style: {
                  display: "inline-flex",
                  alignItems: "center"
                }
              },
              [
                h(IconifyIconOnline, {
                  icon: icon,
                  style: {
                    fontSize: "22px",
                    color: color
                  }
                })
              ]
            )
        }
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
