<script setup lang="ts">
import { defineAsyncComponent, onBeforeMount, ref } from "vue";
import { useChatBotStoreHook } from "@/store/modules/chat";
import { useChatBot } from "@/views/chat/utils/hook";
import { Conversation } from "@/views/chat/utils/type";

const ChatSidebar = defineAsyncComponent(
  () => import("./components/ChatSidebar.vue")
);

const ChatMain = defineAsyncComponent(
  () => import("./components/ChatMain.vue")
);

const useChatBotStore = useChatBotStoreHook();

const chatBot = useChatBot();

const mainView = ref("chat");

const handleSelectedConversation = (conversation: Conversation) => {
  chatBot.setConversation(conversation);
};

const handleNewConversation = () => chatBot.setNewConversation();

const getBotAgents = async () => {
  return await useChatBotStore.getChatBots();
};

onBeforeMount(async () => {
  await getBotAgents();
});
</script>

<template>
  <div class="main flex flex-col" style="height: calc(100% - 25px)">
    <div ref="contentRef" class="flex h-full">
      <ChatSidebar
        :chat-bot="chatBot"
        @show-conversation="handleSelectedConversation"
        @new-conversation="handleNewConversation"
      />
      <ChatMain :main-view="mainView" :chat-bot="chatBot" />
    </div>
  </div>
</template>

<style lang="scss">
.chat-messages::-webkit-scrollbar {
  width: 6px;
}
.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
}
.chat-messages::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.typing-indicator span {
  animation: bounce 1.4s infinite ease-in-out both;
}
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
