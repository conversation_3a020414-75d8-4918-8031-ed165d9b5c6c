/**
 * Utility Classes for ProcMS Widget
 * Atomic CSS utilities for common styling needs
 */

/* === Spacing Utilities === */
.procms-p-0 { padding: 0 !important; }
.procms-p-1 { padding: var(--procms-space-1) !important; }
.procms-p-2 { padding: var(--procms-space-2) !important; }
.procms-p-3 { padding: var(--procms-space-3) !important; }
.procms-p-4 { padding: var(--procms-space-4) !important; }
.procms-p-5 { padding: var(--procms-space-5) !important; }
.procms-p-6 { padding: var(--procms-space-6) !important; }

.procms-px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.procms-px-1 { padding-left: var(--procms-space-1) !important; padding-right: var(--procms-space-1) !important; }
.procms-px-2 { padding-left: var(--procms-space-2) !important; padding-right: var(--procms-space-2) !important; }
.procms-px-3 { padding-left: var(--procms-space-3) !important; padding-right: var(--procms-space-3) !important; }
.procms-px-4 { padding-left: var(--procms-space-4) !important; padding-right: var(--procms-space-4) !important; }

.procms-py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.procms-py-1 { padding-top: var(--procms-space-1) !important; padding-bottom: var(--procms-space-1) !important; }
.procms-py-2 { padding-top: var(--procms-space-2) !important; padding-bottom: var(--procms-space-2) !important; }
.procms-py-3 { padding-top: var(--procms-space-3) !important; padding-bottom: var(--procms-space-3) !important; }
.procms-py-4 { padding-top: var(--procms-space-4) !important; padding-bottom: var(--procms-space-4) !important; }

.procms-m-0 { margin: 0 !important; }
.procms-m-1 { margin: var(--procms-space-1) !important; }
.procms-m-2 { margin: var(--procms-space-2) !important; }
.procms-m-3 { margin: var(--procms-space-3) !important; }
.procms-m-4 { margin: var(--procms-space-4) !important; }
.procms-m-auto { margin: auto !important; }

.procms-mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.procms-mx-1 { margin-left: var(--procms-space-1) !important; margin-right: var(--procms-space-1) !important; }
.procms-mx-2 { margin-left: var(--procms-space-2) !important; margin-right: var(--procms-space-2) !important; }
.procms-mx-3 { margin-left: var(--procms-space-3) !important; margin-right: var(--procms-space-3) !important; }
.procms-mx-4 { margin-left: var(--procms-space-4) !important; margin-right: var(--procms-space-4) !important; }
.procms-mx-auto { margin-left: auto !important; margin-right: auto !important; }

.procms-my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.procms-my-1 { margin-top: var(--procms-space-1) !important; margin-bottom: var(--procms-space-1) !important; }
.procms-my-2 { margin-top: var(--procms-space-2) !important; margin-bottom: var(--procms-space-2) !important; }
.procms-my-3 { margin-top: var(--procms-space-3) !important; margin-bottom: var(--procms-space-3) !important; }
.procms-my-4 { margin-top: var(--procms-space-4) !important; margin-bottom: var(--procms-space-4) !important; }

/* === Display Utilities === */
.procms-block { display: block !important; }
.procms-inline-block { display: inline-block !important; }
.procms-inline { display: inline !important; }
.procms-flex { display: flex !important; }
.procms-inline-flex { display: inline-flex !important; }
.procms-grid { display: grid !important; }
.procms-hidden { display: none !important; }

/* === Flexbox Utilities === */
.procms-flex-row { flex-direction: row !important; }
.procms-flex-row-reverse { flex-direction: row-reverse !important; }
.procms-flex-col { flex-direction: column !important; }
.procms-flex-col-reverse { flex-direction: column-reverse !important; }

.procms-flex-wrap { flex-wrap: wrap !important; }
.procms-flex-nowrap { flex-wrap: nowrap !important; }

.procms-items-start { align-items: flex-start !important; }
.procms-items-end { align-items: flex-end !important; }
.procms-items-center { align-items: center !important; }
.procms-items-baseline { align-items: baseline !important; }
.procms-items-stretch { align-items: stretch !important; }

.procms-justify-start { justify-content: flex-start !important; }
.procms-justify-end { justify-content: flex-end !important; }
.procms-justify-center { justify-content: center !important; }
.procms-justify-between { justify-content: space-between !important; }
.procms-justify-around { justify-content: space-around !important; }
.procms-justify-evenly { justify-content: space-evenly !important; }

.procms-flex-1 { flex: 1 1 0% !important; }
.procms-flex-auto { flex: 1 1 auto !important; }
.procms-flex-initial { flex: 0 1 auto !important; }
.procms-flex-none { flex: none !important; }

.procms-flex-shrink-0 { flex-shrink: 0 !important; }
.procms-flex-shrink { flex-shrink: 1 !important; }

.procms-flex-grow-0 { flex-grow: 0 !important; }
.procms-flex-grow { flex-grow: 1 !important; }

/* === Grid Utilities === */
.procms-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.procms-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.procms-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.procms-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }

.procms-gap-0 { gap: 0 !important; }
.procms-gap-1 { gap: var(--procms-space-1) !important; }
.procms-gap-2 { gap: var(--procms-space-2) !important; }
.procms-gap-3 { gap: var(--procms-space-3) !important; }
.procms-gap-4 { gap: var(--procms-space-4) !important; }

/* === Position Utilities === */
.procms-static { position: static !important; }
.procms-fixed { position: fixed !important; }
.procms-absolute { position: absolute !important; }
.procms-relative { position: relative !important; }
.procms-sticky { position: sticky !important; }

.procms-inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.procms-top-0 { top: 0 !important; }
.procms-right-0 { right: 0 !important; }
.procms-bottom-0 { bottom: 0 !important; }
.procms-left-0 { left: 0 !important; }

/* === Size Utilities === */
.procms-w-auto { width: auto !important; }
.procms-w-full { width: 100% !important; }
.procms-w-screen { width: 100vw !important; }
.procms-w-min { width: min-content !important; }
.procms-w-max { width: max-content !important; }

.procms-h-auto { height: auto !important; }
.procms-h-full { height: 100% !important; }
.procms-h-screen { height: 100vh !important; }
.procms-h-min { height: min-content !important; }
.procms-h-max { height: max-content !important; }

.procms-min-w-0 { min-width: 0 !important; }
.procms-min-w-full { min-width: 100% !important; }
.procms-min-h-0 { min-height: 0 !important; }
.procms-min-h-full { min-height: 100% !important; }

.procms-max-w-none { max-width: none !important; }
.procms-max-w-xs { max-width: 20rem !important; }
.procms-max-w-sm { max-width: 24rem !important; }
.procms-max-w-md { max-width: 28rem !important; }
.procms-max-w-lg { max-width: 32rem !important; }
.procms-max-w-xl { max-width: 36rem !important; }
.procms-max-w-full { max-width: 100% !important; }

/* === Typography Utilities === */
.procms-text-xs { font-size: var(--procms-font-size-xs) !important; }
.procms-text-sm { font-size: var(--procms-font-size-sm) !important; }
.procms-text-base { font-size: var(--procms-font-size-base) !important; }
.procms-text-lg { font-size: var(--procms-font-size-lg) !important; }
.procms-text-xl { font-size: var(--procms-font-size-xl) !important; }
.procms-text-2xl { font-size: var(--procms-font-size-2xl) !important; }

.procms-font-normal { font-weight: var(--procms-font-weight-normal) !important; }
.procms-font-medium { font-weight: var(--procms-font-weight-medium) !important; }
.procms-font-semibold { font-weight: var(--procms-font-weight-semibold) !important; }
.procms-font-bold { font-weight: var(--procms-font-weight-bold) !important; }

.procms-leading-tight { line-height: var(--procms-line-height-tight) !important; }
.procms-leading-normal { line-height: var(--procms-line-height-normal) !important; }
.procms-leading-relaxed { line-height: var(--procms-line-height-relaxed) !important; }

.procms-text-left { text-align: left !important; }
.procms-text-center { text-align: center !important; }
.procms-text-right { text-align: right !important; }
.procms-text-justify { text-align: justify !important; }

.procms-uppercase { text-transform: uppercase !important; }
.procms-lowercase { text-transform: lowercase !important; }
.procms-capitalize { text-transform: capitalize !important; }
.procms-normal-case { text-transform: none !important; }

/* === Color Utilities === */
.procms-text-primary { color: var(--procms-text-primary) !important; }
.procms-text-secondary { color: var(--procms-text-secondary) !important; }
.procms-text-muted { color: var(--procms-text-muted) !important; }
.procms-text-inverse { color: var(--procms-text-inverse) !important; }
.procms-text-success { color: var(--procms-success) !important; }
.procms-text-warning { color: var(--procms-warning) !important; }
.procms-text-error { color: var(--procms-error) !important; }

.procms-bg-primary { background-color: var(--procms-primary) !important; }
.procms-bg-surface { background-color: var(--procms-widget-surface) !important; }
.procms-bg-success { background-color: var(--procms-success) !important; }
.procms-bg-warning { background-color: var(--procms-warning) !important; }
.procms-bg-error { background-color: var(--procms-error) !important; }
.procms-bg-transparent { background-color: transparent !important; }

/* === Border Utilities === */
.procms-border { border: 1px solid var(--procms-widget-border) !important; }
.procms-border-0 { border: 0 !important; }
.procms-border-t { border-top: 1px solid var(--procms-widget-border) !important; }
.procms-border-r { border-right: 1px solid var(--procms-widget-border) !important; }
.procms-border-b { border-bottom: 1px solid var(--procms-widget-border) !important; }
.procms-border-l { border-left: 1px solid var(--procms-widget-border) !important; }

.procms-rounded-none { border-radius: 0 !important; }
.procms-rounded-sm { border-radius: var(--procms-radius-sm) !important; }
.procms-rounded { border-radius: var(--procms-radius-base) !important; }
.procms-rounded-md { border-radius: var(--procms-radius-md) !important; }
.procms-rounded-lg { border-radius: var(--procms-radius-lg) !important; }
.procms-rounded-xl { border-radius: var(--procms-radius-xl) !important; }
.procms-rounded-2xl { border-radius: var(--procms-radius-2xl) !important; }
.procms-rounded-full { border-radius: var(--procms-radius-full) !important; }

/* === Shadow Utilities === */
.procms-shadow-none { box-shadow: none !important; }
.procms-shadow { box-shadow: var(--procms-widget-shadow) !important; }
.procms-shadow-lg { box-shadow: var(--procms-widget-shadow-lg) !important; }

/* === Overflow Utilities === */
.procms-overflow-auto { overflow: auto !important; }
.procms-overflow-hidden { overflow: hidden !important; }
.procms-overflow-visible { overflow: visible !important; }
.procms-overflow-scroll { overflow: scroll !important; }

.procms-overflow-x-auto { overflow-x: auto !important; }
.procms-overflow-x-hidden { overflow-x: hidden !important; }
.procms-overflow-x-scroll { overflow-x: scroll !important; }

.procms-overflow-y-auto { overflow-y: auto !important; }
.procms-overflow-y-hidden { overflow-y: hidden !important; }
.procms-overflow-y-scroll { overflow-y: scroll !important; }

/* === Z-Index Utilities === */
.procms-z-0 { z-index: 0 !important; }
.procms-z-10 { z-index: 10 !important; }
.procms-z-20 { z-index: 20 !important; }
.procms-z-30 { z-index: 30 !important; }
.procms-z-40 { z-index: 40 !important; }
.procms-z-50 { z-index: 50 !important; }
.procms-z-auto { z-index: auto !important; }

/* === Opacity Utilities === */
.procms-opacity-0 { opacity: 0 !important; }
.procms-opacity-25 { opacity: 0.25 !important; }
.procms-opacity-50 { opacity: 0.5 !important; }
.procms-opacity-75 { opacity: 0.75 !important; }
.procms-opacity-100 { opacity: 1 !important; }

/* === Cursor Utilities === */
.procms-cursor-auto { cursor: auto !important; }
.procms-cursor-default { cursor: default !important; }
.procms-cursor-pointer { cursor: pointer !important; }
.procms-cursor-wait { cursor: wait !important; }
.procms-cursor-text { cursor: text !important; }
.procms-cursor-move { cursor: move !important; }
.procms-cursor-help { cursor: help !important; }
.procms-cursor-not-allowed { cursor: not-allowed !important; }

/* === User Select Utilities === */
.procms-select-none { user-select: none !important; }
.procms-select-text { user-select: text !important; }
.procms-select-all { user-select: all !important; }
.procms-select-auto { user-select: auto !important; }

/* === Pointer Events Utilities === */
.procms-pointer-events-none { pointer-events: none !important; }
.procms-pointer-events-auto { pointer-events: auto !important; }

/* === Transition Utilities === */
.procms-transition-none { transition: none !important; }
.procms-transition-all { transition: all var(--procms-transition-base) !important; }
.procms-transition-colors { transition: color var(--procms-transition-base), background-color var(--procms-transition-base), border-color var(--procms-transition-base) !important; }
.procms-transition-opacity { transition: opacity var(--procms-transition-base) !important; }
.procms-transition-shadow { transition: box-shadow var(--procms-transition-base) !important; }
.procms-transition-transform { transition: transform var(--procms-transition-base) !important; }

/* === Transform Utilities === */
.procms-transform { transform: translateX(var(--procms-translate-x, 0)) translateY(var(--procms-translate-y, 0)) rotate(var(--procms-rotate, 0)) skewX(var(--procms-skew-x, 0)) skewY(var(--procms-skew-y, 0)) scaleX(var(--procms-scale-x, 1)) scaleY(var(--procms-scale-y, 1)) !important; }
.procms-transform-gpu { transform: translate3d(var(--procms-translate-x, 0), var(--procms-translate-y, 0), 0) rotate(var(--procms-rotate, 0)) skewX(var(--procms-skew-x, 0)) skewY(var(--procms-skew-y, 0)) scaleX(var(--procms-scale-x, 1)) scaleY(var(--procms-scale-y, 1)) !important; }
.procms-transform-none { transform: none !important; }

.procms-scale-0 { --procms-scale-x: 0; --procms-scale-y: 0; }
.procms-scale-50 { --procms-scale-x: 0.5; --procms-scale-y: 0.5; }
.procms-scale-75 { --procms-scale-x: 0.75; --procms-scale-y: 0.75; }
.procms-scale-90 { --procms-scale-x: 0.9; --procms-scale-y: 0.9; }
.procms-scale-95 { --procms-scale-x: 0.95; --procms-scale-y: 0.95; }
.procms-scale-100 { --procms-scale-x: 1; --procms-scale-y: 1; }
.procms-scale-105 { --procms-scale-x: 1.05; --procms-scale-y: 1.05; }
.procms-scale-110 { --procms-scale-x: 1.1; --procms-scale-y: 1.1; }
.procms-scale-125 { --procms-scale-x: 1.25; --procms-scale-y: 1.25; }
.procms-scale-150 { --procms-scale-x: 1.5; --procms-scale-y: 1.5; }

/* === Responsive Utilities === */
@media (max-width: 768px) {
  .procms-md\:hidden { display: none !important; }
  .procms-md\:block { display: block !important; }
  .procms-md\:flex { display: flex !important; }
  .procms-md\:text-sm { font-size: var(--procms-font-size-sm) !important; }
  .procms-md\:p-2 { padding: var(--procms-space-2) !important; }
  .procms-md\:p-3 { padding: var(--procms-space-3) !important; }
}

@media (max-width: 480px) {
  .procms-sm\:hidden { display: none !important; }
  .procms-sm\:block { display: block !important; }
  .procms-sm\:flex { display: flex !important; }
  .procms-sm\:text-xs { font-size: var(--procms-font-size-xs) !important; }
  .procms-sm\:p-1 { padding: var(--procms-space-1) !important; }
  .procms-sm\:p-2 { padding: var(--procms-space-2) !important; }
}
