import { http } from "@/utils/http";

export type Result<T = any> = {
  success: boolean;
  message: string;
  data: T;
};

export type UserResult<T = any> = {
  success: boolean;
  message: string;
  data: T;
};

export const getLanguages = () => {
  return http.request<Result>("get", "/api/v1/languages");
};

export const getTranslations = (locale: string) => {
  return http.request<Result>("get", `/api/v1/translations/${locale}.json`);
};
