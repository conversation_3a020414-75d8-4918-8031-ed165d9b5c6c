# Sidebar Management Module

## Overview
This module manages the sidebar navigation system, including menu items, their hierarchy, permissions, and display settings.

## Module Structure
```
src/views/system/sidebars/
├── components/
│   ├── SidebarDrawerForm.vue    # Form for creating/editing sidebars
│   └── SidebarFilterForm.vue    # Filter form for sidebar list
├── utils/
│   ├── auth-api.ts              # API functions for CRUD operations
│   ├── columns.tsx              # Table column definitions
│   ├── enums.ts                 # Constants and options
│   ├── hook.ts                  # Business logic and state management
│   └── type.ts                  # TypeScript type definitions
├── index.vue                    # Main sidebar management page
└── README.md                    # This documentation
```

## Field Definitions

### Core Fields
| Field             | Type      | Description                                                                                                       |
| :---------------- | :-------- | :---------------------------------------------------------------------------------------------------------------- |
| `id`              | `number`  | Unique identifier                                                                                                 |
| `menuType`        | `number`  | Menu type (`0` for menu, `1` for iframe, `2` for external link, `3` for button)                                 |
| `parentId`        | `number`  | Parent menu ID for hierarchical structure                                                                        |
| `title`           | `string`  | Menu display name (supports internationalization)                                                                |
| `name`            | `string`  | Route name (must be unique and match component's `defineOptions` name)                                          |
| `path`            | `string`  | Route path                                                                                                        |
| `component`       | `string`  | Component path                                                                                                    |
| `rank`            | `number`  | Menu order (lower numbers appear first)                                                                          |
| `redirect`        | `string`  | Route redirection path                                                                                            |

### Display & Behavior
| Field             | Type      | Description                                                                                                       |
| :---------------- | :-------- | :---------------------------------------------------------------------------------------------------------------- |
| `icon`            | `string`  | Menu icon identifier                                                                                              |
| `extraIcon`       | `string`  | Additional right-side icon                                                                                        |
| `showLink`        | `boolean` | Whether to display this menu in navigation                                                                        |
| `showParent`      | `boolean` | Whether to display the parent menu                                                                                |
| `keepAlive`       | `boolean` | Whether to cache the route page                                                                                   |
| `hiddenTag`       | `boolean` | Whether to hide from tabs                                                                                         |
| `fixedTag`        | `boolean` | Whether to fix in tabs (unclosable)                                                                               |

### Advanced Features
| Field             | Type        | Description                                                                                                     |
| :---------------- | :---------- | :-------------------------------------------------------------------------------------------------------------- |
| `activePath`      | `string`    | Path to activate when this route is accessed (for routes with query/params)                                    |
| `auths`           | `string[]`  | Authorization identifiers for button-level permissions                                                          |
| `frameSrc`        | `string`    | URL for embedded iframe (when menuType = 1)                                                                     |
| `frameLoading`    | `boolean`   | Whether to show loading animation for iframe                                                                     |
| `enterTransition` | `string`    | Page enter animation                                                                                             |
| `leaveTransition` | `string`    | Page leave animation                                                                                             |

### Timestamps
| Field             | Type      | Description                                                                                                       |
| :---------------- | :-------- | :---------------------------------------------------------------------------------------------------------------- |
| `createdAt`       | `string`  | Creation timestamp                                                                                                |
| `updatedAt`       | `string`  | Last update timestamp                                                                                             |
| `deletedAt`       | `string`  | Soft deletion timestamp (nullable)                                                                                |

## API Endpoints

### Read Operations
- `GET /api/auth/sidebars` - Get paginated sidebar list
- `GET /api/auth/sidebars/{id}` - Get sidebar by ID
- `GET /api/auth/sidebars/dropdown` - Get sidebar options for dropdowns

### Create & Update
- `POST /api/auth/sidebars` - Create new sidebar
- `PUT /api/auth/sidebars/{id}` - Update sidebar by ID

### Delete Operations
- `DELETE /api/auth/sidebars/{id}/delete` - Soft delete sidebar
- `DELETE /api/auth/sidebars/bulk/delete` - Bulk soft delete
- `DELETE /api/auth/sidebars/{id}/force` - Permanent delete
- `DELETE /api/auth/sidebars/bulk/force` - Bulk permanent delete

### Restore Operations
- `PUT /api/auth/sidebars/{id}/restore` - Restore soft deleted sidebar
- `PUT /api/auth/sidebars/bulk/restore` - Bulk restore

## Features

### ✅ Implemented
- CRUD operations with proper validation
- Hierarchical menu structure
- Soft delete with restore functionality
- Bulk operations (delete, restore)
- Pagination and sorting
- Advanced filtering
- Type safety with TypeScript
- Internationalization support
- Responsive design

### 🔄 In Progress
- Permission-based access control
- Advanced form validation
- Drag & drop reordering

### 📋 Planned
- Menu preview functionality
- Import/export capabilities
- Audit trail logging
