import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getBots = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/bots", { params });
};

export const getBotById = (id: string) => {
  return http.request<Result>("get", `/api/v1/auth/bots/${id}`);
};

export const getBotsDropdown = () => {
  return http.request<Result>("get", "/api/v1/auth/bots/dropdown");
};

/*
 ***************************
 *   File Upload Operations
 ***************************
 */
export const uploadBotAvatar = (file: File) => {
  const formData = new FormData();
  formData.append("avatar", file);

  return http.request<r>("post", "/api/v1/auth/bots/upload-avatar", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createBot = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/bots", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateBotById = (id: string, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/bots/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteBot = (id: string) => {
  return http.request<Result>("delete", `/api/v1/auth/bots/${id}/delete`);
};

export const bulkDeleteBots = (data: { ids: string[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/bots/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteBotPermanent = (id: string) => {
  return http.request<Result>("delete", `/api/v1/auth/bots/${id}/force`);
};

export const bulkDeleteBotsPermanent = (data: { ids: string[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/bots/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreBot = (id: string) => {
  return http.request<Result>("put", `/api/v1/auth/bots/${id}/restore`);
};

export const bulkRestoreBots = (data: { ids: string[] }) => {
  return http.request<Result>("put", "/api/v1/auth/bots/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const getGeneralPrompts = (params?: object) => {
  return http.request<Result>("get", `/api/v1/auth/bot-general-prompt`, {
    params
  });
};

/*
 ***************************
 *   Knowledge Base Operations
 ***************************
 */
export const getKnowledgeBaseFiles = (params?: object) => {
  return http.request<Result>("get", `/api/v1/auth/knowledge-bases/files`, {
    params
  });
};

export const getRemoveFile = (data: object) => {
  return http.request<Result>(
    "delete",
    `/api/v1/auth/knowledge-bases/files/remove`,
    {
      data
    }
  );
};
