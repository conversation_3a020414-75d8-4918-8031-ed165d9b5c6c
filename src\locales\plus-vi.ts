const Vietnamese = {
  name: "vi",
  plus: {
    dialog: {
      confirmText: "Đồng ý",
      cancelText: "<PERSON>ủ<PERSON>",
      title: "<PERSON>ộ<PERSON> thoại"
    },
    datepicker: {
      startPlaceholder: "<PERSON>ui lòng chọn thời gian bắt đầu",
      endPlaceholder: "<PERSON>ui lòng chọn thời gian kết thúc"
    },
    dialogForm: {
      title: "Biểu mẫu hộp thoại"
    },
    drawerForm: {
      title: "Biểu mẫu ngăn kéo",
      confirmText: "Đồng ý",
      cancelText: "Hủy"
    },
    form: {
      submitText: "Gửi",
      resetText: "Đặt lại",
      errorTip: "Vui lòng hoàn thành biểu mẫu và gửi lại!"
    },
    field: {
      pleaseEnter: "Vui lòng nhập ",
      pleaseSelect: "Vui lòng chọn "
    },
    popover: {
      confirmText: "Đồng ý",
      cancelText: "<PERSON>ủ<PERSON>"
    },
    search: {
      searchText: "T<PERSON><PERSON> kiếm",
      resetText: "Đặt lại",
      expand: "Mở rộng",
      retract: "<PERSON>hu gọn"
    },
    table: {
      title: "Bảng",
      density: "Độ dày",
      refresh: "Làm mới",
      columnSettings: "Cài đặt cột",
      selectAll: "Chọn tất cả",
      default: "Mặc định",
      loose: "Thưa",
      compact: "Dày",
      action: "Thao tác",
      more: "Thêm",
      confirmToPerformThisOperation: "Xác nhận thực hiện thao tác này?",
      prompt: "Thông báo",
      sort: "Sắp xếp"
    },
    stepsForm: {
      nextText: "Bước tiếp",
      preText: "Bước trước",
      submitText: "Gửi"
    },
    inputTag: {
      placeholder: "Vui lòng nhập từ khóa và nhấn enter hoặc phím cách"
    },
    header: {
      logout: "Đăng xuất"
    }
  }
};

export { Vietnamese as default };
