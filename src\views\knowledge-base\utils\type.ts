export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  ownerType?: string;
  ownerId?: number;
  ownerName?: string;
  name?: string;
  type?: "file" | "text" | "url" | "document";
  storagePath?: string;
  content?: string;
  status?: "active" | "inactive" | "processing" | "failed";
  metadata?: string;
  files?: any[]; // Array to store uploaded files
  // Relations
  user?: {
    id: number;
    name: string;
  };
  organization?: {
    id: number;
    name: string;
  };
};

export type KnowledgeBaseFilterProps = {
  name?: string;
  type?: "file" | "text" | "url" | "document" | "";
  status?: "active" | "inactive" | "processing" | "failed" | "";
  ownerType?: string;
  ownerName?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};

export const getFileIcon = (extension: string) => {
  const iconMap = {
    // Documents
    pdf: "mdi:file-pdf",
    doc: "mdi:file-word",
    docx: "mdi:file-word",
    xls: "mdi:file-excel",
    xlsx: "mdi:file-excel",
    ppt: "mdi:file-powerpoint",
    pptx: "mdi:file-powerpoint",
    txt: "mdi:file-document",

    // Images
    jpg: "mdi:file-image",
    jpeg: "mdi:file-image",
    png: "mdi:file-image",
    gif: "mdi:file-image",
    svg: "mdi:file-image",

    // Archives
    zip: "mdi:file-zip",
    rar: "mdi:file-zip",
    "7z": "mdi:file-zip",

    // Others
    json: "mdi:file-code",
    xml: "mdi:file-code",
    csv: "mdi:file-delimited"
  };
  return iconMap[extension] || "mdi:file";
};

export const getFileIconColor = (extension: string) => {
  const colorMap = {
    // Documents
    pdf: "#ef4444", // red-500
    doc: "#3b82f6", // blue-500
    docx: "#3b82f6", // blue-500
    xls: "#22c55e", // green-500
    xlsx: "#22c55e", // green-500
    ppt: "#f97316", // orange-500
    pptx: "#f97316", // orange-500
    txt: "#6b7280", // gray-500
    // Images
    jpg: "#8b5cf6", // violet-500
    jpeg: "#8b5cf6", // violet-500
    png: "#8b5cf6", // violet-500
    gif: "#8b5cf6", // violet-500
    svg: "#8b5cf6", // violet-500
    // Archives
    zip: "#f59e0b", // amber-500
    rar: "#f59e0b", // amber-500
    "7z": "#f59e0b", // amber-500
    // Others
    json: "#6366f1", // indigo-500
    xml: "#6366f1", // indigo-500
    csv: "#10b981" // emerald-500
  };
  return colorMap[extension] || "#6b7280"; // gray-500
};
