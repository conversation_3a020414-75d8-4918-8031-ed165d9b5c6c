import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import { getPlatformConfig } from "./config";
import { MotionPlugin } from "@vueuse/motion";
import { useEcharts } from "@/plugins/echarts";
import { createApp, type Directive } from "vue";
import { useElementPlus } from "@/plugins/elementPlus";
import { injectResponsiveStorage } from "@/utils/responsive";
import i18n from "@/plugins/i18n";

import Table from "@pureadmin/table";
// import PureDescriptions from "@pureadmin/descriptions";

// Import reset styles
import "./style/reset.scss";
// Import common styles
import "./style/index.scss";
// It is necessary to import tailwind.css in main.ts to prevent vite from requesting the entire src/style/index.scss css file on every HMR, which can slow down hot updates.
import "./style/tailwind.css";
import "element-plus/dist/index.css";
// Import font icons
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";

const app = createApp(App);

// Custom directives
import * as directives from "@/directives";
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// Globally register @iconify/vue icon library
import {
  IconifyIconOffline,
  IconifyIconOnline,
  FontIcon
} from "./components/ReIcon";
app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);

// Globally register button-level permission components
import { Auth } from "@/components/ReAuth";
import { Perms } from "@/components/RePerms";
app.component("Auth", Auth);
app.component("Perms", Perms);

// Globally register vue-tippy
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light.css";
import VueTippy from "vue-tippy";
app.use(VueTippy);

import "@/services/soketi.service";

getPlatformConfig(app).then(async config => {
  setupStore(app);
  app.use(router);
  app.use(i18n);
  await router.isReady();
  injectResponsiveStorage(app, config);
  app.use(MotionPlugin).use(useElementPlus).use(Table).use(useEcharts);
  // .use(PureDescriptions)
  app.mount("#app");
});
