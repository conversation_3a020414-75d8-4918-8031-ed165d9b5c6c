<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, watch } from "vue";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { getProvidersDropdown } from "@/views/model-ai/provider/utils/auth-api";
import { getCategoriesDropdown } from "@/views/model-ai/category/utils/auth-api";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();
const providers = ref([]);
const categories = ref([]);

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Model Key")),
    prop: "key",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Model Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Model Provider")),
    prop: "modelProviderId",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: computed(() => [
      ...providers.value.map((provider: any) => ({
        label: provider.name,
        value: provider.id
      }))
    ])
  },
  {
    label: computed(() => $t("Model Category")),
    prop: "modelCategoryId",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: computed(() => [
      ...categories.value.map((category: any) => ({
        label: category.name,
        value: category.id
      }))
    ])
  },
  {
    label: computed(() => $t("Streaming")),
    prop: "streaming",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ]
  },
  {
    label: computed(() => $t("Vision")),
    prop: "vision",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ]
  },
  {
    label: computed(() => $t("Function Calling")),
    prop: "functionCalling",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ]
  },
  {
    label: computed(() => $t("Is Default")),
    prop: "isDefault",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ]
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: ""
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Draft"), value: "draft" }
    ]
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("No"), value: "no" },
      { label: $t("Yes"), value: "yes" }
    ]
  }
];

const handleSubmit = async () => {
  try {
    loading.value = true;
    emit("submit", props.values);
  } catch (error) {
    console.error("Filter submission failed:", error);
  } finally {
    loading.value = false;
  }
};

const loadProviders = async () => {
  await getProvidersDropdown()
    .then(({ data }) => {
      providers.value = useConvertKeyToCamel(data);
    })
    .catch();
};

const loadCategories = async () => {
  await getCategoriesDropdown()
    .then(({ data }) => {
      categories.value = useConvertKeyToCamel(data);
    })
    .catch();
};

watch(
  () => props.visible,
  () => {
    if (props.visible) {
      loadCategories();
      loadProviders();
    }
  }
);

const handleReset = () => {
  emit("reset");
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:filter-2-line')"
          @click="handleSubmit"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}

.custom-footer {
  @apply flex justify-end gap-2;
}
</style>
