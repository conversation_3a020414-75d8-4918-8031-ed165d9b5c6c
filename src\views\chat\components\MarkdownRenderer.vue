<template>
  <div class="markdown-content" v-html="compiledMarkdown" />
</template>

<script>
import { marked } from "marked";
import DOMPurify from "dompurify";

export default {
  name: "MarkdownRenderer",
  props: {
    content: {
      type: String,
      required: true
    }
  },
  computed: {
    compiledMarkdown() {
      if (!this.content || typeof this.content !== "string") {
        return "";
      }

      const rawHtml = marked.parse(this.content);
      return DOMPurify.sanitize(rawHtml);
    }
  }
};
</script>

<style scoped>
.markdown-content {
  line-height: 1.6;
  color: #333;
}

.markdown-content >>> h1,
.markdown-content >>> h2,
.markdown-content >>> h3 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: #2c3e50;
}

.markdown-content >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-content >>> h2 {
  font-size: 1.5em;
}

.markdown-content >>> h3 {
  font-size: 1.3em;
}

.markdown-content >>> ul,
.markdown-content >>> ol {
  padding-left: 2em;
  margin: 0em 0;
}

.markdown-content >>> li {
  margin-bottom: 0.5em;
}

.markdown-content >>> strong {
  font-weight: 600;
  color: #2c3e50;
}

.markdown-content >>> a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s;
}

.markdown-content >>> a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.markdown-content >>> p {
  margin: 0.2em 0;
}
</style>
