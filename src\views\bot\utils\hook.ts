import { reactive, ref, nextTick } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { BotFilterProps, FormItemProps } from "@/views/bot/utils/type";
import {
  getBots,
  deleteBotPermanent,
  bulkDeleteBotsPermanent,
  deleteBot,
  bulkDeleteBots,
  restoreBot,
  bulkRestoreBots,
  createBot,
  updateBotById
} from "@/views/bot/utils/auth-api";

export function useBotHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    name: null,
    description: null,
    status: "active",
    logoUrl: null,
    logo: null,
    knowledge: {
      enabled: true,
      text: "",
      newUploads: [],
      libraryFiles: [],
      botFiles: []
    }
  });
  const botFormRef = ref();
  const filterRef = ref<BotFilterProps>({ isTrashed: "no" });

  /*
   ***************************
   *   API Handlers
   ***************************
   */
  const fnGetBots = async () => {
    loading.value = true;
    try {
      const response = await getBots(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );

      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total || response.data?.length || 0;

    } catch (e) {
      console.error("Get Bot error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (val: any) => {
    console.log("fnHandleSelectionChange--------------->", val);
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetBots();
  };

  const fnHandlePageChange = async () => {
    await fnGetBots();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetBots();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (id: string) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDelete = async (id: string) => {
    try {
      loading.value = true;
      const response = await deleteBot(id.toString());
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Bot chỉ có hard delete, không có soft delete và restore
  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDelete = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteBots({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Bots error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   CTA handlers
   ***************************
   */
  const handleSubmit = async (
    values: FieldValues,
    knowledgeFiles: any,
    clearUploadCallback?: () => void
  ) => {
    // Prepare data for submission
    const submitData = { ...values };
    if (values.uuid != null) {
      const success = await fnHandleUpdateBot(
        values.uuid as string,
        submitData
      );
      if (success) {
        // Clear the file list properly
        knowledgeFiles.value = [];
        if (clearUploadCallback) {
          clearUploadCallback();
        }
        drawerVisible.value = false;
      }
      return success;
    }
    const success = await fnHandleCreateBot(submitData);
    if (success) {
      // Clear the file list properly
      knowledgeFiles.value = [];
      if (clearUploadCallback) {
        clearUploadCallback();
      }
      drawerVisible.value = false;
    }
    return success;
  };

  const fnHandleCreateBot = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createBot(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const fnHandleUpdateBot = async (uuid: string, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateBotById(uuid, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleFilter = async (values: BotFilterProps) => {
    filterRef.value = values;
    await fnGetBots();
  };

  const handleReset = () => {
    drawerValues.value = {
      name: null,
      description: null,
      status: "active",
      logoUrl: null,
      logo: null,
      knowledge: {
        enabled: true,
        text: "",
        newUploads: [],
        libraryFiles: [],
        botFiles: []
      }
    };
    // Use nextTick to ensure component is mounted before calling resetForm
    nextTick(() => {
      if (botFormRef?.value?.resetForm) {
        botFormRef.value.resetForm();
      }
    });
  };

  /*
   ***************************
   *   Permanent Delete handlers
   ***************************
   */
  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteBotPermanent(row.uuid.toString());
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetBots();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting bot:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async (ids?: string[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.uuid);
    if (selectedIds.length === 0) {
      message($t("Please select items to permanently delete"), {
        type: "warning"
      });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteBotsPermanent({ ids: selectedIds });
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetBots();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting bots:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  /*
   ***************************
   *   Restore handlers
   ***************************
   */
  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await restoreBot(row.uuid.toString());
      message($t("Restored successfully"), { type: "success" });
      await fnGetBots();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error restoring bot:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  const handleBulkRestore = async (ids?: string[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.uuid);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await bulkRestoreBots({ ids: selectedIds });
      message($t("Restored successfully"), { type: "success" });
      await fnGetBots();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk restoring bots:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  /*
   ***************************
   *   Form Handlers
   ***************************
   */

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    botFormRef,

    // API Handlers
    fnGetBots,
    fnHandleCreateBot,
    fnHandleUpdateBot,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter,
    handleReset
  };
}
