<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Language Code")),
    prop: "code",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Language Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Native Name")),
    prop: "nativeName",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Direction")),
    prop: "direction",
    valueType: "select",
    options: [
      { label: $t("Left to Right"), value: "ltr" },
      { label: $t("Right to Left"), value: "rtl" }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Is Default")),
    prop: "isDefault",
    valueType: "select",
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: $t("Yes"), value: "yes" },
      { label: $t("No"), value: "no" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    }
  }
];

const handleSubmit = async () => {
  try {
    loading.value = true;
    emit("submit", props.values);
  } catch (error) {
    console.error("Filter submission failed:", error);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  emit("reset");
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}

.custom-footer {
  @apply flex justify-end gap-2;
}
</style>
