import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { h } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "key",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Category key")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Category Name")
  },
  {
    prop: "icon",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Icon"),
    formatter: ({ icon }) => {
      return h(
        IconifyIconOnline,
        { icon: icon, width: "24px", class: "mx-auto" },
        null
      );
    }
  },
  {
    prop: "status",
    sortable: false,
    align: "center",
    width: 160,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        active: {
          type: "success",
          text: $t("Active"),
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600"
        },
        inactive: {
          type: "danger",
          text: $t("Inactive"),
          class: "bg-red-100 text-red-800",
          icon: "ri:close-circle-fill",
          iconClass: "text-red-600"
        }
      };

      const config = statusColors[row.status] || statusColors.active;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
