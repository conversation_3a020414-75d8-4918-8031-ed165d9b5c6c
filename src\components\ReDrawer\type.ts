import type { CSSProperties, VNode, Component } from "vue";

type DoneFn = (cancel?: boolean) => void;
type EventType = "open" | "close" | "openAutoFocus" | "closeAutoFocus";
type ArgsType = {
  /** `cancel` for clicking the cancel button, `sure` for clicking the confirm button, `close` for other closing actions (e.g., top-right button, overlay, ESC key) */
  command: "cancel" | "sure" | "close";
};

type ButtonType =
  | "primary"
  | "success"
  | "warning"
  | "danger"
  | "info"
  | "text";

type DrawerProps = {
  /** Controls the visibility of the Drawer */
  visible?: boolean;
  /** Whether to append the Drawer to the `body` element. Nested Drawers must set this to `true`. Default is `false` */
  appendToBody?: boolean;
  /** The DOM element to mount to. This will override `appendToBody` */
  appendTo?: string;
  /** Whether to lock the body scroll when the Drawer is visible. Default is `true` */
  lockScroll?: boolean;
  /** A callback function executed before the Drawer closes. It can prevent the Drawer from closing. The Drawer will only close when the `done` function is called. */
  beforeClose?: (done: DoneFn) => void;
  /** Whether the Drawer can be closed by clicking the modal overlay. Default is `true` */
  closeOnClickModal?: boolean;
  /** Whether the Drawer can be closed by pressing the ESC key. Default is `true` */
  closeOnPressEscape?: boolean;
  /** Whether to show the close button. Default is `true` */
  showClose?: boolean;
  /** Delay before opening the Drawer, in milliseconds. Default is `0` */
  openDelay?: number;
  /** Delay before closing the Drawer, in milliseconds. Default is `0` */
  closeDelay?: number;
  /** Custom class name for the Drawer */
  class?: string;
  /** Custom style for the Drawer */
  style?: CSSProperties;
  /** Whether to destroy the child elements when the Drawer is closed. Default is `false` */
  destroyOnClose?: boolean;
  /** Whether a modal overlay is required. Default is `true` */
  modal?: boolean;
  /** The direction in which the Drawer opens. Default is `rtl` */
  direction?: "rtl" | "ltr" | "ttb" | "btt";
  /** The size of the Drawer. If a `number` is used, it's in pixels. If a `string` is used, please use a percentage like `'x%'`. */
  size?: string | number;
  /** The title of the Drawer */
  title?: string;
  /** Whether to show the header. Default is `true`. When set to `false`, the `title` attribute and `title` slot are not effective. */
  withHeader?: boolean;
  /** Custom class name for the modal overlay */
  modalClass?: string;
  /** Sets the `z-index` */
  zIndex?: number;
  /** The `aria-level` attribute for the header. Default is `2` */
  headerAriaLevel?: string;
};

// See: https://element-plus.org/en-US/component/pop-confirm.html#attributes
type PopConfirm = {
  /** Title */
  title?: string;
  /** Text for the confirm button */
  confirmButtonText?: string;
  /** Text for the cancel button */
  cancelButtonText?: string;
  /** Type of the confirm button. Default is `primary` */
  confirmButtonType?: ButtonType;
  /** Type of the cancel button. Default is `text` */
  cancelButtonType?: ButtonType;
  /** Custom icon. Default is `QuestionFilled` */
  icon?: string | Component;
  /** Icon color. Default is `#f90` */
  iconColor?: string;
  /** Whether to hide the Icon. Default is `false` */
  hideIcon?: boolean;
  /** Delay before closing, in milliseconds. Default is `200` */
  hideAfter?: number;
  /** Whether to append the popover dropdown to the `body` element. Default is `true` */
  teleported?: boolean;
  /** When the popover is not triggered for a long time and `persistent` is `false`, the popover will be destroyed. Default is `false` */
  persistent?: boolean;
  /** Popover width, minimum width is `150px`. Default is `150` */
  width?: string | number;
};

type BtnClickDrawer = {
  options?: DrawerOptions;
  index?: number;
};
type BtnClickButton = {
  btn?: ButtonProps;
  index?: number;
};
/** See: https://element-plus.org/en-US/component/button.html#button-attributes */
type ButtonProps = {
  /** Button text */
  label: string;
  /** Button size */
  size?: "large" | "default" | "small";
  /** Button type */
  type?: "primary" | "success" | "warning" | "danger" | "info";
  /** Whether it is a plain button. Default is `false` */
  plain?: boolean;
  /** Whether it is a text button. Default is `false` */
  text?: boolean;
  /** Whether to show background color for the text button. Default is `false` */
  bg?: boolean;
  /** Whether it is a link button. Default is `false` */
  link?: boolean;
  /** Whether it is a round button. Default is `false` */
  round?: boolean;
  /** Whether it is a circle button. Default is `false` */
  circle?: boolean;
  /** PopConfirm configuration for the button */
  popConfirm?: PopConfirm;
  /** Whether it is in a loading state. Default is `false` */
  loading?: boolean;
  /** Custom loading icon component */
  loadingIcon?: string | Component;
  /** Whether the button is disabled. Default is `false` */
  disabled?: boolean;
  /** Icon component */
  icon?: string | Component;
  /** Whether to enable native `autofocus`. Default is `false` */
  autofocus?: boolean;
  /** Native `type` attribute. Default is `button` */
  nativeType?: "button" | "submit" | "reset";
  /** Automatically insert a space between two Chinese characters */
  autoInsertSpace?: boolean;
  /** Custom button color, which automatically calculates hover and active colors */
  color?: string;
  /** Dark mode, which automatically sets the color for dark mode. Default is `false` */
  dark?: boolean;
  /** Custom element tag */
  tag?: string | Component;
  /** Callback triggered after clicking the button */
  btnClick?: ({
    drawer,
    button
  }: {
    /** Current Drawer information */
    drawer: BtnClickDrawer;
    /** Current button information */
    button: BtnClickButton;
  }) => void;
};

interface DrawerOptions extends DrawerProps {
  /** Props for the content component, which can be received via `defineProps` */
  props?: any;
  /** Whether to hide the footer button area of the Drawer */
  hideFooter?: boolean;
  /** PopConfirm configuration for the confirm button */
  popConfirm?: PopConfirm;
  /** Whether to enable loading animation on the confirm button after clicking */
  sureBtnLoading?: boolean;
  /**
   * @description Custom renderer for the Drawer title content
   * @see {@link https://element-plus.org/en-US/component/drawer.html#slots}
   */
  headerRenderer?: ({
    close,
    titleId,
    titleClass
  }: {
    close: Function;
    titleId: string;
    titleClass: string;
  }) => VNode | Component;
  /** Custom content renderer */
  contentRenderer?: ({
    options,
    index
  }: {
    options: DrawerOptions;
    index: number;
  }) => VNode | Component;
  /** Custom renderer for the footer button area, which will override `footerButtons` and the default `Cancel` and `Confirm` buttons */
  footerRenderer?: ({
    options,
    index
  }: {
    options: DrawerOptions;
    index: number;
  }) => VNode | Component;
  /** Custom footer buttons */
  footerButtons?: Array<ButtonProps>;
  /** Callback after the Drawer is opened */
  open?: ({
    options,
    index
  }: {
    options: DrawerOptions;
    index: number;
  }) => void;
  /** Callback after the Drawer is closed (only triggered by clicking the top-right close button, the modal overlay, or pressing the ESC key) */
  close?: ({
    options,
    index
  }: {
    options: DrawerOptions;
    index: number;
  }) => void;
  /** Callback after the Drawer is closed. The `command` value in `args` can be: `cancel`, `sure`, or `close`. */
  closeCallBack?: ({
    options,
    index,
    args
  }: {
    options: DrawerOptions;
    index: number;
    args: any;
  }) => void;
  /** Callback when the focus enters the Drawer content */
  openAutoFocus?: ({
    options,
    index
  }: {
    options: DrawerOptions;
    index: number;
  }) => void;
  /** Callback when the focus leaves the Drawer content */
  closeAutoFocus?: ({
    options,
    index
  }: {
    options: DrawerOptions;
    index: number;
  }) => void;

  /** Callback for clicking the cancel button. It can prevent the Drawer from closing. The Drawer will only close when the `done` function is called. */
  beforeCancel?: (
    done: Function,
    {
      options,
      index
    }: {
      options: DrawerOptions;
      index: number;
    }
  ) => void;
  /** Callback for clicking the confirm button. It can prevent the Drawer from closing. The Drawer will only close when the `done` function is called. */
  beforeSure?: (
    done: Function,
    {
      options,
      index,
      closeLoading
    }: {
      options: DrawerOptions;
      index: number;
      closeLoading: Function;
    }
  ) => void;
}

export type { ButtonProps, DrawerOptions, ArgsType, DrawerProps, EventType };
