import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, SidebarFilterProps } from "./type";
import {
  getSidebars,
  getSidebarsDropdown,
  createSidebar,
  updateSidebarById,
  deleteSidebar,
  deleteSidebarPermanent,
  bulkDeleteSidebars,
  bulkDeleteSidebarsPermanent,
  restoreSidebar,
  bulkRestoreSidebars
} from "./auth-api";

export function useSidebarHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<SidebarFilterProps>({ isTrashed: false });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const sidebarsDropdown = ref([]);

  // Form refs
  const sidebarFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    menuType: 0
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetSidebars = async () => {
    loading.value = true;
    try {
      const params = {
        ...filterRef.value,
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        sort_by: sort.value.sortBy,
        sort_order: sort.value.sortOrder
      };
      const res = await getSidebars(params);
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total || 0;
    } catch (error) {
      console.error("Error fetching sidebars:", error);
      message($t("Failed to fetch data"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnGetSidebarsDropdown = async () => {
    try {
      const res = await getSidebarsDropdown();
      sidebarsDropdown.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching sidebars dropdown:", error);
    }
  };

  /* ***************************
   * CRUD Operations
   *************************** */

  const fnHandleCreateSidebar = async (formData: FormItemProps) => {
    try {
      loading.value = true;
      const response = await createSidebar(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), { type: "success" });
        await fnGetSidebars();
        return true;
      }
      message(response.message || $t("Create failed"), { type: "error" });
      return false;
    } catch (error) {
      console.error("Create sidebar error:", error);
      message($t("Create failed"), { type: "error" });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateSidebar = async (id: number, formData: FormItemProps) => {
    try {
      loading.value = true;
      const response = await updateSidebarById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), { type: "success" });
        await fnGetSidebars();
        return true;
      }
      message(response.message || $t("Update failed"), { type: "error" });
      return false;
    } catch (error) {
      console.error("Update sidebar error:", error);
      message($t("Update failed"), { type: "error" });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = (column: any) => {
    sort.value.sortBy = column.prop;
    sort.value.sortOrder = column.order === "ascending" ? "asc" : "desc";
    fnGetSidebars();
  };

  const fnHandlePaginationChange = () => {
    fnGetSidebars();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetSidebars();
  };

  /* ***************************
   * Delete Operations
   *************************** */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      loading.value = true;
      const response = await deleteSidebar(row.id);
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetSidebars();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Delete sidebar error:", error);
      message($t("Delete failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      loading.value = true;
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeleteSidebars({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetSidebars();
        multipleSelection.value = [];
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Bulk delete error:", error);
      message($t("Delete failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Form Handlers
   *************************** */

  const handleSubmit = async (values: FormItemProps) => {
    if (values.id != null) {
      const success = await fnHandleUpdateSidebar(Number(values.id), values);
      if (success) {
        drawerVisible.value = false;
        drawerValues.value = { menuType: 0 };
        sidebarFormRef.value?.resetForm();
      }
      return;
    }
    const success = await fnHandleCreateSidebar(values);
    if (success) {
      drawerVisible.value = false;
      drawerValues.value = { menuType: 0 };
      sidebarFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: SidebarFilterProps) => {
    filterRef.value = values;
    pagination.currentPage = 1;
    await fnGetSidebars();
  };

  /* ***************************
   * Lifecycle
   *************************** */

  onMounted(() => {
    fnGetSidebars();
    fnGetSidebarsDropdown();
  });

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    // Data/State
    loading,
    filterRef,
    records,
    multipleSelection,
    pagination,
    sort,
    sidebarsDropdown,
    // Form refs
    filterVisible,
    drawerVisible,
    drawerValues,
    sidebarFormRef,
    // Event handlers
    handleDelete,
    handleBulkDelete,
    fnGetSidebars,
    fnGetSidebarsDropdown,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePaginationChange,
    fnHandleSizeChange,
    // Form related
    handleSubmit,
    handleFilter
  };
}
