import Cookies from "js-cookie";
import { useUserStoreHook } from "@/store/modules/user";
import { isIncludeAllChildren, isString, storageLocal } from "@pureadmin/utils";

export interface DataInfo<T> {
  /** token */
  accessToken: string;
  expires: T;
  refreshToken: string;
  avatar?: string;
  username?: string;
  nickname?: string;
  roles?: Array<string>;
  permissions?: Array<string>;
}

export interface LoginResponseData {
  user: {
    username: string;
    firstName: string;
    lastName: string;
    fullName: string;
    avatar: string | null;
    birthday: string | null;
    gender: string;
    email: string;
    emailVerifiedAt: string | null;
    phone: string | null;
    phoneVerifiedAt: string | null;
    address: string | null;
    status: string;
    lastLoginAt: string | null;
    lastLoginIp: string | null;
    preferences: any;
    isVerified: boolean;
    newsletterSubscribed: boolean;
    createdAt: string;
    updatedAt: string;
    permissions: Array<string>;
    roles: Array<string>;
    geoDivision: any;
    country: any;
  };
  token: string;
  tokenType: string;
  expiresIn: number;
}

export const userKey = "user-info";
export const TokenKey = "authorized-token";

export const multipleTabsKey = "multiple-tabs";

/** Get `token` */
export function getToken(): DataInfo<number> {
  // Same as `TokenKey`, this approach solves the error when `TokenKey` does not exist in `Cookies` during initialization
  return Cookies.get(TokenKey)
    ? JSON.parse(Cookies.get(TokenKey))
    : storageLocal().getItem<Record<string, any>>(userKey)?.refreshToken;
}

/**
 * @description Set `token` and necessary info using seamless refresh `token` strategy
 * Seamless refresh: Backend returns `accessToken` (token used for accessing interfaces), `refreshToken` (token required for calling refresh `accessToken` interface, `refreshToken` expiration time (e.g. 30 days) should be greater than `accessToken` expiration time (e.g. 2 hours)), `expires` (`accessToken` expiration time)
 * Put `accessToken`, `expires`, `refreshToken` these three pieces of info in cookie with key value authorized-token (automatically destroyed when expired)
 * Put `avatar`, `username`, `nickname`, `roles`, `permissions`, `refreshToken`, `expires` these seven pieces of info in localStorage with key value `user-info` (automatically destroyed when browser is completely closed using `multipleTabsKey`)
 */
export function setToken(data: DataInfo<Date> | LoginResponseData) {
  let expires = 0;
  let accessToken: string;
  let refreshToken: string;
  const { isRemembered, loginDay } = useUserStoreHook();

  // Check if data is from new login API response format
  if ("token" in data && "user" in data) {
    // New API format
    const loginData = data as LoginResponseData;
    accessToken = loginData.token;
    // Check if API provides separate refresh token, otherwise use access token
    refreshToken = (loginData as any).refreshToken || loginData.token;
    expires = Date.now() + loginData.expiresIn * 1000; // Convert seconds to milliseconds and add to current time
  } else {
    // Old format
    const oldData = data as DataInfo<Date>;
    accessToken = oldData.accessToken;
    refreshToken = oldData.refreshToken;
    expires = new Date(oldData.expires).getTime();
  }

  const cookieString = JSON.stringify({ accessToken, expires, refreshToken });

  expires > 0
    ? Cookies.set(TokenKey, cookieString, {
        expires: Math.max((expires - Date.now()) / 86400000, 0.001) // Ensure at least 1.44 minutes
      })
    : Cookies.set(TokenKey, cookieString);

  Cookies.set(
    multipleTabsKey,
    "true",
    isRemembered
      ? {
          expires: loginDay
        }
      : {}
  );

  function setUserKey(userData: any) {
    // Update user store state directly
    const userStore = useUserStoreHook();
    userStore.user = {
      ...userData,
      isRemembered,
      loginDay
    };

    // Store in localStorage with all user data
    storageLocal().setItem(userKey, {
      refreshToken,
      expires,
      ...userData,
      isRemembered,
      loginDay
    });
  }

  // Handle new API format
  if ("token" in data && "user" in data) {
    const loginData = data as LoginResponseData;
    const user = loginData.user;
    // Pass all user data, not just selected fields
    setUserKey({
      ...user,
      nickname: user.fullName || user.firstName + " " + user.lastName,
      avatar: user.avatar || ""
    });
  } else {
    // Handle old format
    const oldData = data as DataInfo<Date>;
    if (oldData.username && oldData.roles) {
      const { username, roles } = oldData;
      setUserKey({
        avatar: oldData?.avatar ?? "",
        username,
        nickname: oldData?.nickname ?? "",
        roles,
        permissions: oldData?.permissions ?? []
      });
    } else {
      const avatar =
        storageLocal().getItem<DataInfo<number>>(userKey)?.avatar ?? "";
      const username =
        storageLocal().getItem<DataInfo<number>>(userKey)?.username ?? "";
      const nickname =
        storageLocal().getItem<DataInfo<number>>(userKey)?.nickname ?? "";
      const roles =
        storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? [];
      const permissions =
        storageLocal().getItem<DataInfo<number>>(userKey)?.permissions ?? [];
      setUserKey({
        avatar,
        username,
        nickname,
        roles,
        permissions
      });
    }
  }
}

/** Delete `token` and localStorage info with key value `user-info` */
export function removeToken() {
  Cookies.remove(TokenKey);
  Cookies.remove(multipleTabsKey);
  storageLocal().removeItem(userKey);
}

/** Format token (jwt format) */
export const formatToken = (token: string): string => {
  return "Bearer " + token;
};

/** Whether has button-level permission (determined based on `permissions` field returned by login interface) */
export const hasPerms = (value: string | Array<string>): boolean => {
  if (!value) return false;
  const allPerms = "*:*:*";
  const { user } = useUserStoreHook();
  const permissions = user?.permissions || [];
  if (!permissions) return false;
  if (permissions.length === 1 && permissions[0] === allPerms) return true;
  return isString(value)
    ? permissions.includes(value)
    : isIncludeAllChildren(value, permissions);
};
