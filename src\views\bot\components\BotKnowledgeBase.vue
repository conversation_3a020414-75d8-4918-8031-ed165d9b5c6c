<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch, nextTick } from "vue";
import { $t } from "@/plugins/i18n";
import { UploadFilled, Document, Delete, Plus } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
import {
  getKnowledgeBaseFiles,
  getRemoveFile
} from "@/views/bot/utils/auth-api";
import { ElMessageBox, ElMessage, TableInstance } from "element-plus";
import {
  getFileTypeIcon,
  getFileTypeLabel,
  getFileTypeColor
} from "../utils/type";

const props = defineProps<{
  drawerValues: any;
  loading: boolean;
  fileLibrary: any[];
  selectedFiles: string[];
}>();

const emit = defineEmits<{
  "update:file-library": [value: any[]];
  "update:selected-files": [value: string[]];
  "update:knowledge-text": [value: string];
  "update:new-uploads": [value: any[]];
  "update:library-files": [value: string[]];
  "update:bot-files": [value: any[]];
  "delete-file": [fileId: string];
  "refresh-library": [];
}>();

const libraryTableRef = ref<TableInstance>();
const knowledgeTab = ref("files");
const knowledgeFiles = ref([]);
const fileFilter = reactive({
  name: "",
  dateFrom: null,
  dateTo: null
});
const isLibraryInitialized = ref(false);

// Dialog state
const showLibraryDialog = ref(false);
const selectedFilesForAdd = ref([]);

// Helper function to format date
const formatDate = (dateString: string) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
};

const loadFileLibrary = async () => {
  try {
    const response = await getKnowledgeBaseFiles({
      name: fileFilter.name,
      dateFrom: fileFilter.dateFrom,
      dateTo: fileFilter.dateTo
    });
    emit("update:file-library", response.data);
  } catch (e) {
    console.error("Get Knowledge base files error:", e);
  }
};

const clearFilters = () => {
  fileFilter.name = "";
  fileFilter.dateFrom = null;
  fileFilter.dateTo = null;
  loadFileLibrary();
};

const handleFileChange = () => {
  // Don't emit files directly as they are UploadFiles, not the correct format
  // knowledge.newUploads should only contain {name, storage_path} objects
  // This will be handled in handleUploadSuccess when server responds
};

const handleUploadSuccess = (response: any, file: any) => {
  const currentNewUploads = props.drawerValues.knowledge?.newUploads || [];
  const newUpload = {
    name: response.data.name || file.name,
    storagePath: response.data.storage_path,
    size: file.size,
    type: file.type
  };
  const updatedNewUploads = [...currentNewUploads, newUpload];
  console.log("Handle Upload Success::::->>>", updatedNewUploads);
  emit("update:new-uploads", updatedNewUploads);

  // Refresh library to show new uploaded file
  emit("refresh-library");

  ElMessage.success($t("File uploaded successfully"));
};

const handleFileSelection = (files: any) => {
  const updatedLibraryFiles = files.map((file: any) => file.uuid);
  emit("update:library-files", updatedLibraryFiles);
};

// Computed property to get current bot files from library
const currentBotFiles = computed(() => {
  const libraryFiles = props.drawerValues.knowledge?.libraryFiles || [];
  return props.fileLibrary.filter(file => libraryFiles.includes(file.uuid));
});

// Check if file is already in bot knowledge
const isFileAlreadyInBot = computed(() => {
  return (fileUuid: string) => {
    const libraryFiles = props.drawerValues.knowledge?.libraryFiles || [];
    return libraryFiles.includes(fileUuid);
  };
});

// Dialog functions
const openLibraryDialog = () => {
  showLibraryDialog.value = true;
  selectedFilesForAdd.value = [];
};

const handleDialogSelection = (files: any) => {
  selectedFilesForAdd.value = files;
};

const confirmAddFiles = () => {
  const currentLibraryFiles = props.drawerValues.knowledge?.libraryFiles || [];
  const newFileUuids = selectedFilesForAdd.value
    .map((file: any) => file.uuid)
    .filter((uuid: string) => !currentLibraryFiles.includes(uuid)); // Filter out duplicates

  if (newFileUuids.length === 0) {
    ElMessage.warning($t("Selected files are already added to this bot"));
    return;
  }

  const updatedLibraryFiles = [...currentLibraryFiles, ...newFileUuids];

  emit("update:library-files", updatedLibraryFiles);
  showLibraryDialog.value = false;
  selectedFilesForAdd.value = [];

  ElMessage.success(
    $t("Added {count} files to bot knowledge", { count: newFileUuids.length })
  );
};

const cancelDialog = () => {
  showLibraryDialog.value = false;
  selectedFilesForAdd.value = [];
};

const removeFileFromBot = (file: any) => {
  const currentLibraryFiles = props.drawerValues.knowledge?.libraryFiles || [];
  const updatedLibraryFiles = currentLibraryFiles.filter(
    (uuid: string) => uuid !== file.uuid
  );
  emit("update:library-files", updatedLibraryFiles);
  ElMessage.success($t("File removed from bot knowledge"));
};

const beforeRemove = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      $t("Are you sure you want to remove this file?"),
      $t("Warning"),
      {
        confirmButtonText: $t("OK"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    );

    // If file was successfully uploaded, remove it from server
    if (file.status === "success" && file.response?.data) {
      try {
        // Use id if available, otherwise use uuid, otherwise use storage_path
        const fileIdentifier =
          file.response.data.id ||
          file.response.data.uuid ||
          file.response.data.storage_path;
        await getRemoveFile({ id: fileIdentifier });

        // Also remove from newUploads if it exists there
        const currentNewUploads =
          props.drawerValues.knowledge?.newUploads || [];
        const updatedNewUploads = currentNewUploads.filter(
          (upload: any) =>
            upload.storage_path !== file.response.data.storage_path
        );
        emit("update:new-uploads", updatedNewUploads);

        ElMessage.success($t("File removed successfully"));
      } catch (error) {
        console.error("Error removing file from server:", error);
        ElMessage.error($t("Failed to remove file from server"));
        return false;
      }
    }

    return true;
  } catch {
    return false;
  }
};

const syncLibrarySelection = () => {
  if (!libraryTableRef.value) return;

  libraryTableRef.value.clearSelection();
  const selectedUUIDs = props.drawerValues.knowledge?.libraryFiles || [];
  selectedUUIDs.forEach((uuid: string) => {
    const row = props.fileLibrary.find(f => f.uuid === uuid);
    if (row) {
      libraryTableRef.value!.toggleRowSelection(row, true);
    }
  });
};

watch(
  knowledgeTab,
  newTab => {
    if (newTab === "library" && !isLibraryInitialized.value) {
      nextTick(() => {
        syncLibrarySelection();
        isLibraryInitialized.value = true;
      });
    }
  },
  { immediate: true }
);
</script>
<template>
  <div class="card">
    <h2 class="section-title">{{ $t("Knowledge Base") }}</h2>
    <el-tabs v-model="knowledgeTab" type="border-card">
      <el-tab-pane :label="$t('Upload New Files')" name="files">
        <el-upload
          ref="uploadRef"
          v-model:file-list="knowledgeFiles"
          class="w-full"
          drag
          action="/api/v1/auth/knowledge-bases/files/upload"
          multiple
          :auto-upload="true"
          :headers="{
            Authorization: `Bearer ${getToken().accessToken ?? getToken()}`,
            'X-Requested-With': 'XMLHttpRequest'
          }"
          :before-remove="beforeRemove"
          @change="handleFileChange"
          @success="handleUploadSuccess"
        >
          <el-icon class="el-icon--upload">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text">
            {{ $t("Drag files here or") }}
            <em>{{ $t("click to upload") }}</em>
          </div>
        </el-upload>
      </el-tab-pane>

      <el-tab-pane :label="$t('Text')" name="text">
        <el-input
          :model-value="props.drawerValues.knowledge.text"
          type="textarea"
          :rows="10"
          :placeholder="$t('Paste text content here.')"
          @update:model-value="val => emit('update:knowledge-text', val)"
        />
      </el-tab-pane>

      <el-tab-pane :label="$t('Document Library')" name="library">
        <div class="flex justify-between items-center mb-4">
          <p class="text-sm text-gray-500">
            {{ $t("Files currently attached to this bot's knowledge base.") }}
          </p>
          <el-button type="primary" :icon="Plus" @click="openLibraryDialog">
            {{ $t("Add Files") }}
          </el-button>
        </div>

        <div
          v-if="!currentBotFiles.length"
          class="text-center py-8 text-gray-500"
        >
          <el-icon class="text-4xl mb-2">
            <Document />
          </el-icon>
          <p>{{ $t("No files attached to this bot yet.") }}</p>
          <p class="text-xs mt-2">
            {{ $t("Click 'Add Files' to select files from your library.") }}
          </p>
        </div>

        <el-table v-else :data="currentBotFiles" height="300">
          <el-table-column prop="name" :label="$t('File Name')" />
          <el-table-column :label="$t('Type')" width="120">
            <template #default="{ row }">
              <div class="flex items-center gap-2">
                <el-icon :color="getFileTypeColor(row.name)">
                  <component :is="getFileTypeIcon(row.name)" />
                </el-icon>
                <span class="text-xs">{{ getFileTypeLabel(row.name) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" :label="$t('Size')" width="120" />
          <el-table-column :label="$t('Upload Date')" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Actions')" width="100" align="center">
            <template #default="{ row }">
              <el-popconfirm
                :title="$t('Remove this file from bot knowledge?')"
                @confirm="removeFileFromBot(row)"
              >
                <template #reference>
                  <el-button type="danger" size="small" :icon="Delete" circle />
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>

  <!-- Add Files Dialog -->
  <el-dialog
    v-model="showLibraryDialog"
    :title="$t('Add Files from Library')"
    width="800px"
    @close="cancelDialog"
  >
    <div class="mb-4">
      <p class="text-sm text-gray-500 mb-4">
        {{
          $t("Select files from your library to add to this bot's knowledge.")
        }}
      </p>

      <!-- Filter Controls -->
      <div class="mb-4 p-3 bg-gray-50 rounded-lg">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ $t("File Name") }}
            </label>
            <el-input
              v-model="fileFilter.name"
              :placeholder="$t('Search by file name...')"
              clearable
              size="small"
              @input="loadFileLibrary"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ $t("From Date") }}
            </label>
            <el-date-picker
              v-model="fileFilter.dateFrom"
              type="date"
              :placeholder="$t('Select start date')"
              size="small"
              style="width: 100%"
              @change="loadFileLibrary"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ $t("To Date") }}
            </label>
            <el-date-picker
              v-model="fileFilter.dateTo"
              type="date"
              :placeholder="$t('Select end date')"
              size="small"
              style="width: 100%"
              @change="loadFileLibrary"
            />
          </div>
        </div>
        <div class="flex justify-between items-center mt-3">
          <span class="text-xs text-gray-500">
            {{
              $t("Total files: {count}", { count: props.fileLibrary.length })
            }}
          </span>
          <el-button size="small" @click="clearFilters">
            {{ $t("Clear Filters") }}
          </el-button>
        </div>
      </div>
    </div>

    <el-table
      v-loading="props.loading"
      :data="props.fileLibrary"
      height="300"
      row-key="uuid"
      @selection-change="handleDialogSelection"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="row => !isFileAlreadyInBot(row.uuid)"
      />
      <el-table-column :label="$t('File Name')">
        <template #default="{ row }">
          <div class="flex items-center gap-2">
            <span :class="{ 'text-gray-400': isFileAlreadyInBot(row.uuid) }">
              {{ row.name }}
            </span>
            <el-tag
              v-if="isFileAlreadyInBot(row.uuid)"
              size="small"
              type="info"
            >
              {{ $t("Already Added") }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Type')" width="120">
        <template #default="{ row }">
          <div class="flex items-center gap-2">
            <el-icon :color="getFileTypeColor(row.name)">
              <component :is="getFileTypeIcon(row.name)" />
            </el-icon>
            <span class="text-xs">{{ getFileTypeLabel(row.name) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="size" :label="$t('Size')" width="120" />
      <el-table-column :label="$t('Upload Date')" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="cancelDialog">{{ $t("Cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="!selectedFilesForAdd.length"
          @click="confirmAddFiles"
        >
          {{ $t("Add Selected Files") }} ({{ selectedFilesForAdd.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
