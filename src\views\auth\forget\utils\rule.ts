import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

// Custom email validator with proper parameter replacement
const emailValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Email is required")));
    return;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    callback(new Error($t("Invalid email format")));
    return;
  }

  callback();
};

const forgotPasswordRules = reactive<FormRules>({
  email: [
    {
      validator: emailValidator,
      trigger: "blur"
    }
  ]
});

export { forgotPasswordRules };
