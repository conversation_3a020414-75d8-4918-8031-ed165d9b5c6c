<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Role name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Display name")),
    prop: "displayName",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ]
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("No"), value: "no" },
      { label: $t("Yes"), value: "yes" }
    ]
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3s
  }
};

const handleReset = () => {
  resetForm();
  emit("update:values", {});
  emit("reset"); // Emit reset event instead of reloading data
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:filter-3-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
