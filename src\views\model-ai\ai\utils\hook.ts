import { reactive, ref } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, ModelAiFilterProps } from "./type";
import {
  getModelAi,
  getModelAiDropdown,
  createModelAi,
  updateModelAiById,
  bulkDeleteModelAi,
  deleteModelAi,
  deleteModelAiPermanent,
  bulkDeleteModelAiPermanent,
  restoreModelAi,
  bulkRestoreModelAi,
  updateOrCreateModelService
} from "./auth-api";

export function useModelAiHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<ModelAiFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const modelAiDropdown = ref([]);

  // Form refs
  const modelAiFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    streaming: false,
    functionCalling: false,
    vision: false,
    isDefault: false,
    status: "active"
  });

  // Service management state
  const serviceVisible = ref<boolean>(false);
  const serviceValues = ref<any>({});
  const serviceFormRef = ref();

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetModelAi = async () => {
    try {
      loading.value = true;
      const res = await getModelAi(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching modelAi:", error);
      message($t("Failed to fetch modelAi"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnGetModelAiDropdown = async () => {
    try {
      const res = await getModelAiDropdown();
      modelAiDropdown.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching modelAi dropdown:", error);
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = async () => {
    await fnGetModelAi();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetModelAi();
  };

  const fnHandleSortChange = async (val: Record<string, any>) => {
    sort.value = {
      sortBy: val.prop,
      sortOrder: val.order == "ascending" ? "asc" : "desc"
    };
    await fnGetModelAi();
  };

  const fnHandleCreateModelAi = async (data: any) => {
    try {
      loading.value = true;
      const response = await createModelAi(data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetModelAi();
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateModelAi = async (id: number, data: any) => {
    try {
      loading.value = true;
      const response = await updateModelAiById(id, data);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetModelAi();
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteModelAi(row.id);
      message($t("Deleted successfully"), { type: "success" });
      await fnGetModelAi();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting modelAi:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteModelAi({ ids: selectedIds });
      message($t("Deleted successfully"), { type: "success" });
      await fnGetModelAi();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting modelAi:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteModelAiPermanent(row.id);
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetModelAi();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting modelAi:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteModelAiPermanent({ ids: selectedIds });
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetModelAi();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting modelAi:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await restoreModelAi(row.id);
      message($t("Restored successfully"), { type: "success" });
      await fnGetModelAi();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error restoring modelAi:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  const handleBulkRestore = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await bulkRestoreModelAi({ ids: selectedIds });
      message($t("Restored successfully"), { type: "success" });
      await fnGetModelAi();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk restoring modelAi:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };
  /*
   ***************************
   *   Form handlers and actions
   ***************************
   */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...row };
    drawerVisible.value = true;
  };

  const handleFilter = async (values: ModelAiFilterProps) => {
    filterRef.value = values;
    await fnGetModelAi();
  };

  const handleSubmit = async (values: FieldValues) => {
    let success = false;
    if (values.id != null) {
      success = await fnHandleUpdateModelAi(Number(values.id), values);
    } else {
      success = await fnHandleCreateModelAi(values);
      if (success) {
        drawerValues.value = { status: "active" };
        modelAiFormRef.value?.resetForm();
      }
    }
  };

  /*
   ***************************
   *   Service handlers and actions
   ***************************
   */

  const handleServiceSubmit = async (values: any) => {
    try {
      console.log("handleServiceSubmit called with values:", values);
      console.log("allowedParameters:", values.allowedParameters);
      console.log("defaultParameters:", values.defaultParameters);

      const { success } = await updateOrCreateModelService(values);

      if (success) {
        message($t("Service saved successfully"), { type: "success" });
        await fnGetModelAi();
        // Don't auto-close form, let user close manually to preserve data
      }
    } catch (error) {
      console.error("Error saving service:", error);
      message($t("Service save failed"), { type: "error" });
    }
  };

  const handleServiceClose = () => {
    serviceFormRef.value?.resetForm();
    // Reset serviceValues when form closes
    serviceValues.value = {};
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    modelAiDropdown,
    handleBulkDelete,
    handleDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,
    fnGetModelAi,
    fnGetModelAiDropdown,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    modelAiFormRef,
    handleSubmit,
    handleFilter,
    handleEdit,
    // Service state and handlers
    serviceVisible,
    serviceValues,
    serviceFormRef,
    handleServiceSubmit,
    handleServiceClose
  };
}
