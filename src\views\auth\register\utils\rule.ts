import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";
import { useSettingStoreHook } from "@/store/modules/settings";

// Custom validators with proper parameter replacement
const emailValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Email is required")));
    return;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    callback(new Error($t("Please enter a valid email address")));
    return;
  }

  callback();
};

const usernameValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Username is required")));
    return;
  }

  const minLength = 3;
  const maxLength = 20;

  if (value.length < minLength) {
    callback(
      new Error($t("Username too short (min {count})", { count: minLength }))
    );
    return;
  }

  if (value.length > maxLength) {
    callback(
      new Error($t("Username too long (max {count})", { count: maxLength }))
    );
    return;
  }

  // Check for valid username characters (letters, numbers, underscore, hyphen)
  if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
    callback(new Error($t("Invalid username format")));
    return;
  }

  callback();
};

const passwordValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Password is required")));
    return;
  }

  // Get dynamic settings with proper structure
  const settingStore = useSettingStoreHook();
  const security = settingStore.settings?.security || {};
  const minLength = security.minPasswordLength || 6;
  const requireNumbers = security.requireNumbers || false;
  const requireSymbols = security.requireSymbols || false;
  const requireUppercase = security.requireUppercase || false;
  const requireLowercase = security.requireLowercase || false;

  // Check minimum length
  if (value.length < minLength) {
    callback(
      new Error($t("Password too short (min {count})", { count: minLength }))
    );
    return;
  }

  // Check for numbers if required
  if (requireNumbers && !/\d/.test(value)) {
    callback(new Error($t("Password needs numbers")));
    return;
  }

  // Check for symbols if required
  if (requireSymbols && !/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
    callback(new Error($t("Password needs symbols")));
    return;
  }

  // Check for uppercase if required
  if (requireUppercase && !/[A-Z]/.test(value)) {
    callback(new Error($t("Password needs uppercase")));
    return;
  }

  // Check for lowercase if required
  if (requireLowercase && !/[a-z]/.test(value)) {
    callback(new Error($t("Password needs lowercase")));
    return;
  }

  callback();
};

const nameValidator =
  (fieldName: string) => (_rule: any, value: string, callback: Function) => {
    if (!value || value.trim() === "") {
      callback(new Error($t("{field} is required", { field: $t(fieldName) })));
      return;
    }

    const minLength = 1;
    const maxLength = 50;
    const trimmedValue = value.trim();

    if (trimmedValue.length < minLength) {
      callback(
        new Error(
          $t("{field} must be at least {count} characters", {
            field: $t(fieldName),
            count: minLength
          })
        )
      );
      return;
    }

    if (trimmedValue.length > maxLength) {
      callback(
        new Error(
          $t("{field} must be less than {count} characters", {
            field: $t(fieldName),
            count: maxLength
          })
        )
      );
      return;
    }

    // Check for valid name characters (letters and spaces)
    if (!/^[\p{L}\s]+$/u.test(trimmedValue)) {
      callback(
        new Error(
          $t("{field} contains invalid characters", { field: $t(fieldName) })
        )
      );
      return;
    }

    callback();
  };

const confirmPasswordValidator =
  (form: any) => (_rule: any, value: string, callback: Function) => {
    if (!value || value.trim() === "") {
      callback(new Error($t("Confirm password required")));
      return;
    }

    if (form.password && form.password !== value) {
      callback(new Error($t("Passwords don't match")));
      return;
    }

    callback();
  };

const termsValidator = (_rule: any, value: boolean, callback: Function) => {
  if (!value) {
    callback(new Error($t("Accept terms required")));
    return;
  }

  callback();
};

const registerRules = (form: any) => {
  return reactive<FormRules>({
    username: [
      {
        validator: usernameValidator,
        trigger: "blur"
      }
    ],
    email: [
      {
        validator: emailValidator,
        trigger: "blur"
      }
    ],
    password: [
      {
        validator: passwordValidator,
        trigger: "blur"
      }
    ],
    confirmPassword: [
      {
        validator: confirmPasswordValidator(form),
        trigger: "blur"
      }
    ],
    firstName: [
      {
        validator: nameValidator("First Name"),
        trigger: "blur"
      }
    ],
    lastName: [
      {
        validator: nameValidator("Last Name"),
        trigger: "blur"
      }
    ],
    acceptTerms: [
      {
        validator: termsValidator,
        trigger: "change"
      }
    ]
  });
};

export { registerRules };
