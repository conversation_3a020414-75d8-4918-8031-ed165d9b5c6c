# Migration Guide

This guide helps you migrate from older versions of the ProcMS Chatbot Widget to the latest version.

## Version 2.0.0 Migration

### Overview

Version 2.0.0 introduces significant improvements including:
- Hybrid widget/iframe architecture
- Enhanced theme system with CSS variables
- Improved API client with retry logic
- Better TypeScript support
- New event system
- Performance optimizations

### Breaking Changes

#### 1. Class Names

**Old (v1.x):**
```javascript
import { ProcmsWidget } from '@procms/chatbot-widget';
const widget = new ProcmsWidget(config);
```

**New (v2.x):**
```javascript
import { ProcmsChatbotWidget, ProcmsChatbot } from '@procms/chatbot-widget';
const widget = new ProcmsChatbotWidget(config);
// or
const widget = await ProcmsChatbot.create(config, '#container');
```

#### 2. Configuration Structure

**Old (v1.x):**
```javascript
const config = {
  bot: 'bot-uuid-123',
  key: 'pk_live_api_key',
  container: '#chatbot',
  style: {
    theme: 'light',
    primaryColor: '#007bff'
  },
  callbacks: {
    onMessage: (msg) => console.log(msg),
    onError: (err) => console.error(err)
  }
};
```

**New (v2.x):**
```javascript
const config = {
  botUuid: 'bot-uuid-123',
  apiKey: 'pk_live_api_key',
  theme: 'light',
  onMessage: (msg) => console.log(msg),
  onError: (err) => console.error(err)
};
```

#### 3. Initialization Methods

**Old (v1.x):**
```javascript
const widget = new ProcmsWidget(config);
widget.init();
```

**New (v2.x):**
```javascript
// Method 1: Create and mount separately
const widget = new ProcmsChatbotWidget(config);
await widget.mount('#container');

// Method 2: Create and mount in one call
const widget = await ProcmsChatbot.create(config, '#container');

// Method 3: Floating widget
const widget = await ProcmsChatbot.createFloatingWidget(config);
```

#### 4. Event System

**Old (v1.x):**
```javascript
widget.onMessage = (message) => console.log(message);
widget.onError = (error) => console.error(error);
```

**New (v2.x):**
```javascript
widget.on('message', (message) => console.log(message));
widget.on('error', (error) => console.error(error));

// Or in config
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  onMessage: (message) => console.log(message),
  onError: (error) => console.error(error)
});
```

#### 5. Theme System

**Old (v1.x):**
```javascript
widget.setStyle({
  theme: 'dark',
  primaryColor: '#ff6b6b',
  backgroundColor: '#1a1a1a'
});
```

**New (v2.x):**
```javascript
// Built-in themes
widget.setTheme('dark');

// Custom themes
widget.setCustomTheme({
  primaryColor: '#ff6b6b',
  backgroundColor: '#1a1a1a',
  textPrimary: '#ffffff'
});
```

#### 6. CSS Classes

**Old (v1.x):**
```css
.procms-widget { /* styles */ }
.procms-header { /* styles */ }
.procms-message { /* styles */ }
```

**New (v2.x):**
```css
.procms-chatbot-widget { /* styles */ }
.procms-widget-header { /* styles */ }
.procms-message { /* styles */ }
```

### Step-by-Step Migration

#### Step 1: Update Dependencies

```bash
# Uninstall old version
npm uninstall @procms/widget

# Install new version
npm install @procms/chatbot-widget@^2.0.0
```

#### Step 2: Update Imports

**Before:**
```javascript
import ProcmsWidget from '@procms/widget';
import '@procms/widget/dist/style.css';
```

**After:**
```javascript
import { ProcmsChatbotWidget, ProcmsChatbot } from '@procms/chatbot-widget';
import '@procms/chatbot-widget/dist/style.css';
```

#### Step 3: Update Configuration

Create a migration helper function:

```javascript
function migrateConfig(oldConfig) {
  const newConfig = {
    botUuid: oldConfig.bot,
    apiKey: oldConfig.key,
    theme: oldConfig.style?.theme || 'light',
    showHeader: oldConfig.showHeader !== false,
    showAvatar: oldConfig.showAvatar !== false,
    autoOpen: oldConfig.autoOpen || false
  };

  // Migrate callbacks
  if (oldConfig.callbacks) {
    if (oldConfig.callbacks.onMessage) {
      newConfig.onMessage = oldConfig.callbacks.onMessage;
    }
    if (oldConfig.callbacks.onError) {
      newConfig.onError = oldConfig.callbacks.onError;
    }
    if (oldConfig.callbacks.onReady) {
      newConfig.onReady = oldConfig.callbacks.onReady;
    }
  }

  // Migrate custom styles
  if (oldConfig.style) {
    const customTheme = {};
    if (oldConfig.style.primaryColor) {
      customTheme.primaryColor = oldConfig.style.primaryColor;
    }
    if (oldConfig.style.backgroundColor) {
      customTheme.backgroundColor = oldConfig.style.backgroundColor;
    }
    if (Object.keys(customTheme).length > 0) {
      newConfig.customTheme = customTheme;
    }
  }

  return newConfig;
}

// Usage
const oldConfig = { /* your old config */ };
const newConfig = migrateConfig(oldConfig);
const widget = new ProcmsChatbotWidget(newConfig);
```

#### Step 4: Update Initialization Code

**Before:**
```javascript
const widget = new ProcmsWidget({
  bot: 'bot-123',
  key: 'pk_live_xxx',
  container: '#chatbot-container'
});
widget.init();
```

**After:**
```javascript
const widget = await ProcmsChatbot.create({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx'
}, '#chatbot-container');
```

#### Step 5: Update Event Handlers

**Before:**
```javascript
widget.onMessage = (message) => {
  console.log('New message:', message);
};

widget.onConversationStart = (id) => {
  console.log('Conversation started:', id);
};
```

**After:**
```javascript
widget.on('message', (message) => {
  console.log('New message:', message);
});

widget.on('conversation-started', (id) => {
  console.log('Conversation started:', id);
});
```

#### Step 6: Update CSS Customizations

**Before:**
```css
.procms-widget {
  border-radius: 12px;
}

.procms-header {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.procms-message.user {
  background: #007bff;
}
```

**After:**
```css
.procms-chatbot-widget {
  --procms-widget-border-radius: 12px;
}

.procms-widget-header {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.procms-message--user .procms-message__content {
  background: var(--procms-primary);
}
```

#### Step 7: Update Cleanup Code

**Before:**
```javascript
// Component unmount
widget.destroy();
```

**After:**
```javascript
// Component unmount
widget.unmount(); // or widget.destroy() for complete cleanup
```

### Framework-Specific Migration

#### React Migration

**Before (v1.x):**
```jsx
import React, { useEffect, useRef } from 'react';
import ProcmsWidget from '@procms/widget';

const ChatWidget = ({ botId, apiKey }) => {
  const containerRef = useRef();
  const widgetRef = useRef();

  useEffect(() => {
    widgetRef.current = new ProcmsWidget({
      bot: botId,
      key: apiKey,
      container: containerRef.current
    });
    widgetRef.current.init();

    return () => {
      widgetRef.current.destroy();
    };
  }, [botId, apiKey]);

  return <div ref={containerRef} />;
};
```

**After (v2.x):**
```jsx
import React, { useEffect, useRef } from 'react';
import { ProcmsChatbot } from '@procms/chatbot-widget';

const ChatWidget = ({ botUuid, apiKey }) => {
  const containerRef = useRef();
  const widgetRef = useRef();

  useEffect(() => {
    const loadWidget = async () => {
      widgetRef.current = await ProcmsChatbot.create({
        botUuid,
        apiKey
      }, containerRef.current);
    };

    loadWidget();

    return () => {
      if (widgetRef.current) {
        widgetRef.current.unmount();
      }
    };
  }, [botUuid, apiKey]);

  return <div ref={containerRef} />;
};
```

#### Vue Migration

**Before (v1.x):**
```vue
<template>
  <div ref="container"></div>
</template>

<script>
import ProcmsWidget from '@procms/widget';

export default {
  props: ['botId', 'apiKey'],
  mounted() {
    this.widget = new ProcmsWidget({
      bot: this.botId,
      key: this.apiKey,
      container: this.$refs.container
    });
    this.widget.init();
  },
  beforeDestroy() {
    if (this.widget) {
      this.widget.destroy();
    }
  }
};
</script>
```

**After (v2.x):**
```vue
<template>
  <div ref="container"></div>
</template>

<script>
import { ProcmsChatbot } from '@procms/chatbot-widget';

export default {
  props: ['botUuid', 'apiKey'],
  async mounted() {
    this.widget = await ProcmsChatbot.create({
      botUuid: this.botUuid,
      apiKey: this.apiKey
    }, this.$refs.container);
  },
  beforeDestroy() {
    if (this.widget) {
      this.widget.unmount();
    }
  }
};
</script>
```

### Common Migration Issues

#### Issue 1: Widget Not Loading

**Problem:** Widget fails to load after migration.

**Solution:**
```javascript
// Check configuration
console.log('Config:', widget.getConfig());

// Check for errors
widget.on('error', (error) => {
  console.error('Widget error:', error);
});

// Verify API key format
if (!apiKey.startsWith('pk_live_') && !apiKey.startsWith('pk_test_')) {
  console.error('Invalid API key format');
}
```

#### Issue 2: CSS Styles Not Applied

**Problem:** Custom styles don't work after migration.

**Solution:**
```css
/* Increase specificity if needed */
.procms-chatbot-widget.procms-chatbot-widget {
  /* Your custom styles */
}

/* Or use CSS variables */
.procms-chatbot-widget {
  --procms-primary: #your-color;
  --procms-widget-border-radius: 12px;
}
```

#### Issue 3: Events Not Firing

**Problem:** Event handlers don't work.

**Solution:**
```javascript
// Make sure to use new event system
widget.on('message', (message) => {
  console.log('Message received:', message);
});

// Check if widget is properly initialized
widget.on('ready', () => {
  console.log('Widget is ready for events');
});
```

### Testing Your Migration

#### 1. Functionality Test

```javascript
// Test basic functionality
const widget = await ProcmsChatbot.create({
  botUuid: 'test-bot-uuid',
  apiKey: 'pk_test_api_key'
}, '#test-container');

// Test events
widget.on('ready', () => console.log('✓ Widget ready'));
widget.on('message', (msg) => console.log('✓ Message event:', msg));
widget.on('error', (err) => console.error('✗ Error:', err));
```

#### 2. Theme Test

```javascript
// Test theme switching
widget.setTheme('dark');
console.log('✓ Dark theme applied');

widget.setCustomTheme({
  primaryColor: '#ff6b6b',
  backgroundColor: '#f8f9fa'
});
console.log('✓ Custom theme applied');
```

#### 3. Cleanup Test

```javascript
// Test proper cleanup
widget.unmount();
console.log('✓ Widget unmounted');

// Verify no memory leaks
console.log('Widget instance:', widget.isMounted()); // Should be false
```

### Performance Improvements in v2.0

1. **Lazy Loading**: Components load only when needed
2. **CSS Optimization**: Smaller bundle size with tree shaking
3. **Better Caching**: Improved API response caching
4. **Memory Management**: Better cleanup and garbage collection
5. **Rendering Optimization**: Virtual scrolling for long conversations

### Getting Help

If you encounter issues during migration:

1. **Check the Console**: Look for error messages and warnings
2. **Review Documentation**: Check the [API Reference](./api-reference.md)
3. **Use Debug Mode**: Enable debug logging with `window.PROCMS_DEBUG = true`
4. **Contact Support**: Reach out to our support team with specific error details

### Migration Checklist

- [ ] Updated package dependencies
- [ ] Updated import statements
- [ ] Migrated configuration structure
- [ ] Updated initialization code
- [ ] Migrated event handlers
- [ ] Updated CSS customizations
- [ ] Updated cleanup code
- [ ] Tested basic functionality
- [ ] Tested theme switching
- [ ] Tested error handling
- [ ] Verified no console errors
- [ ] Tested on target browsers/devices

### Next Steps

After successful migration:

1. Explore new features like enhanced themes
2. Implement new event handlers for better analytics
3. Consider using the new iframe mode for better isolation
4. Review performance improvements
5. Update your documentation and team training

For more information, see:
- [API Reference](./api-reference.md)
- [Integration Examples](./examples/)
- [Troubleshooting Guide](./troubleshooting.md)

## Conclusion

Version 2.0.0 brings significant improvements in performance, developer experience, and functionality. While the migration requires some code changes, the new architecture provides better stability, customization options, and future-proofing for your chatbot integration.

Take advantage of the new features like enhanced theming, better error handling, and improved mobile support to create a better user experience.
