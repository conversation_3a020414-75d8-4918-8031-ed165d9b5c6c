
# Dashboard Module API Documentation

## Tổng quan

Module Dashboard cung cấp các API endpoints để lấy thống kê và dữ liệu dashboard cá nhân cho người dùng đã đăng nhập. Module này hỗ trợ caching để tối ưu hiệu suất.

## Base URL
```
/api/v1/auth
```

## Authentication
Tất cả APIs yêu cầu JWT token trong header:
```
Authorization: Bearer {token}
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Dashboard data retrieved successfully",
  "data": { ... },
  "cached": false,
  "cache_expires_at": "2024-01-01T12:00:00.000000Z"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "data": null
}
```

## API Endpoints

### 1. Get Complete Dashboard Data

**GET** `/dashboard/data`

L<PERSON><PERSON> toàn bộ dữ liệu dashboard bao gồm thống kê và biểu đồ.

#### Query Parameters
- `period` (optional): `day|week|month|year` - Kho<PERSON>ng thời gian thống kê
- `timezone` (optional): Timezone identifier
- `include_charts` (optional): `boolean` - Có bao gồm dữ liệu biểu đồ
- `include_stats` (optional): `boolean` - Có bao gồm thống kê

#### Response Example
```json
{
  "success": true,
  "message": "Dashboard data retrieved successfully",
  "data": {
    "stats": {
      "chatbot": {
        "total": 12,
        "active": 8,
        "draft": 4,
        "mostUsedBot": "Customer Support Bot"
      },
      "conversation": {
        "total": 245,
        "today": 18,
        "week": 89,
        "avgConversationLength": 12.5
      },
      "token": {
        "total": 125680,
        "estimatedCost": "15.75"
      },
      "knowledge": {
        "totalDocuments": 45,
        "fileTypes": {
          "pdf": 25,
          "docx": 12,
          "txt": 8,
          "other": 5
        },
        "mostQueriedDoc": "Product Manual.pdf"
      },
      "storage": {
        "totalUsedMB": 245.8,
        "documentsSizeMB": 125.5,
        "attachmentsSizeMB": 120.3,
        "remainingQuotaMB": 754.2,
        "quotaLimitMB": 1000,
        "usagePercent": 24.6
      }
    },
    "charts": {
      "tokenTrend": {
        "labels": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        "datasets": [
          {
            "label": "Input Tokens",
            "data": [1200, 1500, 1800, 1400, 1600, 1900, 1250],
            "borderColor": "#3B82F6",
            "backgroundColor": "rgba(59, 130, 246, 0.1)",
            "tension": 0.4
          },
          {
            "label": "Output Tokens",
            "data": [2100, 2400, 2800, 2200, 2600, 3100, 2050],
            "borderColor": "#10B981",
            "backgroundColor": "rgba(16, 185, 129, 0.1)",
            "tension": 0.4
          }
        ]
      },
      "conversationTrend": {
        "labels": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        "datasets": [
          {
            "label": "Conversations",
            "data": [12, 18, 15, 22, 19, 25, 18],
            "borderColor": "#8B5CF6",
            "backgroundColor": "rgba(139, 92, 246, 0.1)",
            "tension": 0.4
          }
        ]
      }
    },
    "metadata": {
      "lastUpdated": "2024-01-01T12:00:00.000000Z",
      "timezone": "UTC",
      "currency": "USD"
    }
  },
  "cached": false,
  "cache_expires_at": "2024-01-01T12:05:00.000000Z"
}
```

### 2. Get Statistics Only

**GET** `/dashboard/stats`

Lấy chỉ dữ liệu thống kê (không bao gồm biểu đồ).

#### Response Example
```json
{
  "success": true,
  "message": "Dashboard statistics retrieved successfully",
  "data": {
    "chatbot": { ... },
    "conversation": { ... },
    "token": { ... },
    "knowledge": { ... },
    "storage": { ... }
  }
}
```

### 3. Get Charts Only

**GET** `/dashboard/charts`

Lấy chỉ dữ liệu biểu đồ (không bao gồm thống kê).

#### Response Example
```json
{
  "success": true,
  "message": "Dashboard charts data retrieved successfully",
  "data": {
    "tokenTrend": { ... },
    "conversationTrend": { ... }
  }
}
```

### 4. Get Summary

**GET** `/dashboard/summary`

Lấy tóm tắt nhanh các chỉ số chính.

#### Response Example
```json
{
  "success": true,
  "message": "Dashboard summary retrieved successfully",
  "data": {
    "totalBots": 12,
    "totalConversations": 245,
    "totalTokens": 125680,
    "totalDocuments": 45,
    "storageUsedPercent": 24.6
  }
}
```

## Data Structure Interfaces

### Stats Interface
```typescript
interface Stats {
  chatbot: {
    total: number;
    active: number;
    draft: number;
    mostUsedBot: string;
  };
  conversation: {
    total: number;
    today: number;
    week: number;
    avgConversationLength: number;
  };
  token: {
    total: number;
    estimatedCost: string;
  };
  knowledge: {
    totalDocuments: number;
    fileTypes: {
      pdf: number;
      docx: number;
      txt: number;
      other: number;
    };
    mostQueriedDoc: string;
  };
  storage: {
    totalUsedMB: number;
    documentsSizeMB: number;
    attachmentsSizeMB: number;
    remainingQuotaMB: number;
    quotaLimitMB: number;
    usagePercent: number;
  };
}
```

### Charts Interface
```typescript
interface ChartData {
  tokenTrend: {
    labels: string[];
    datasets: ChartDataset[];
  };
  conversationTrend: {
    labels: string[];
    datasets: ChartDataset[];
  };
}

interface ChartDataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
  tension: number;
}
```

## Caching

- Dashboard data được cache trong 5 phút
- Cache key: `dashboard_data_user_{user_id}`
- Response bao gồm thông tin cache: `cached` và `cache_expires_at`

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing or invalid token)
- `500` - Internal Server Error

## Rate Limiting

Áp dụng rate limiting chuẩn của hệ thống cho authenticated users.

## Frontend Integration

### Component Usage
Dashboard welcome component được tích hợp với API service:

```typescript
// src/api/dashboard.ts
import { DashboardAPI, mapTimeFilterToPeriod } from "@/api/dashboard";

// Load dashboard data
const period = mapTimeFilterToPeriod(timeFilter.value); // "7d" -> "week"
const result = await DashboardAPI.getDashboardData(period);
```

### Time Filter Mapping
- `7d` → `week`
- `30d` → `month`
- `3m` → `quarter`

### UI Components
- **Hero Header**: Gradient background với time filter controls
- **Metrics Cards**: 4 cards hiển thị key metrics với Tailwind CSS
- **Charts Section**: ECharts integration với responsive design
- **Insights Section**: Knowledge base và storage usage với progress bars

### Styling
- **Framework**: Tailwind CSS v4
- **Components**: Element Plus
- **Charts**: ECharts
- **Responsive**: Mobile-first design

### Development Mode
Sử dụng mock data từ `getMockDashboardData()` khi backend chưa sẵn sàng.
