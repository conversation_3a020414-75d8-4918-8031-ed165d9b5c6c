export type Language = {
  id: number;
  code: string;
  name: string;
  nativeName: string;
  direction: string;
  flag?: string;
  isActive: boolean;
  isDefault: boolean;
  status: "active" | "inactive";
  sortOrder?: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type FormItemProps = {
  id?: number | null;
  code?: string;
  name?: string;
  nativeName?: string;
  direction?: string;
  flag?: string;
  isActive?: boolean;
  isDefault?: boolean;
  status?: "active" | "inactive";
  sortOrder?: number;
  [key: string]: any;
};

export type LanguageFilterProps = {
  code?: string;
  name?: string;
  nativeName?: string;
  direction?: string;
  isActive?: boolean;
  isDefault?: boolean;
  status?: "active" | "inactive";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
