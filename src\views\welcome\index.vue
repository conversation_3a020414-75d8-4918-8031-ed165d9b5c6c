<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, onUnmounted } from "vue";
import { ElRadioGroup, ElRadioButton, ElButton } from "element-plus";
import * as echarts from "echarts";
import type { EChartsOption, ECharts } from "echarts";
import { DashboardAPI, mapTimeFilterToPeriod } from "@/api/dashboard";
import type { DashboardStats, DashboardCharts } from "@/api/dashboard";

// Use types from API service
type Stats = DashboardStats;
type ChartData = DashboardCharts;

// --- COMPONENT LOGIC ---
const loading = ref(true);
const timeFilter = ref("7d");
const data = reactive<{ stats: Partial<Stats>; charts: Partial<ChartData> }>({
  stats: {
    storage: {
      quotaLimitMB: 1,
      totalUsedMB: 0,
      documentsSizeMB: 0,
      attachmentsSizeMB: 0,
      remainingQuotaMB: 1
    }
  }, // Default values to avoid division by zero
  charts: {}
});

// --- CHART REFS & INSTANCES ---
const tokenChartRef = ref<HTMLElement>();
const conversationChartRef = ref<HTMLElement>();
const knowledgeChartRef = ref<HTMLElement>();
const storageChartRef = ref<HTMLElement>();

let tokenChart: ECharts | undefined;
let conversationChart: ECharts | undefined;
let knowledgeChart: ECharts | undefined;
let storageChart: ECharts | undefined;

// --- COMPUTED PROPERTIES ---
const storageUsagePercentage = computed(() => {
  const storage = data.stats.storage;
  if (!storage || !storage.quotaLimitMB) return 0;
  return Math.round((storage.totalUsedMB / storage.quotaLimitMB) * 100);
});

const formatSize = (sizeMB?: number) => {
  if (!sizeMB) return "0 MB";
  if (sizeMB < 1024) return `${sizeMB.toFixed(1)} MB`;
  return `${(sizeMB / 1024).toFixed(1)} GB`;
};

const storageUsedFormatted = computed(() =>
  formatSize(data.stats.storage?.totalUsedMB)
);
const storageQuotaFormatted = computed(() =>
  formatSize(data.stats.storage?.quotaLimitMB)
);
const storageRemainingFormatted = computed(() =>
  formatSize(data.stats.storage?.remainingQuotaMB)
);

// --- METHODS ---
const loadDashboardData = async () => {
  loading.value = true;
  try {
    // Real API call
    const period = mapTimeFilterToPeriod(timeFilter.value);
    const result = await DashboardAPI.getDashboardData(period);

    if (result.success) {
      Object.assign(data, result.data);
    } else {
      throw new Error(result.message || "Failed to load dashboard data");
    }
  } catch (error) {
    console.error("Failed to load dashboard data:", error);
    // Handle error appropriately - could show error message to user
  }
  loading.value = false;

  // Wait for DOM to update before initializing charts
  await nextTick();
  initAllCharts();
};

const handleTimeFilterChange = (filter: string | number | boolean) => {
  timeFilter.value = filter as string;
  loadDashboardData();
};

const exportData = () => {
  // In a real app, you'd use a library like 'file-saver' or 'xlsx'
  alert("Chức năng xuất báo cáo đang được phát triển!");
};

const initAllCharts = () => {
  if (tokenChartRef.value && data.charts.tokenTrend) {
    tokenChart = echarts.init(tokenChartRef.value);
    const tokenData = data.charts.tokenTrend;
    tokenChart.setOption({
      title: {
        text: "Token Usage Trend",
        textStyle: { fontSize: 16, fontWeight: "normal" },
        left: "center"
      },
      tooltip: { trigger: "axis" },
      legend: {
        data: tokenData.datasets.map(d => d.label),
        top: 30
      },
      grid: { top: 70, right: 20, bottom: 30, left: 50 },
      xAxis: { type: "category", data: tokenData.labels },
      yAxis: { type: "value" },
      series: tokenData.datasets.map(dataset => ({
        name: dataset.label,
        type: "line",
        smooth: true,
        data: dataset.data,
        itemStyle: { color: dataset.borderColor }
      }))
    } as EChartsOption);
  }

  if (conversationChartRef.value && data.charts.conversationTrend) {
    conversationChart = echarts.init(conversationChartRef.value);
    const conversationData = data.charts.conversationTrend;
    conversationChart.setOption({
      title: {
        text: "Conversation Volume",
        textStyle: { fontSize: 16, fontWeight: "normal" },
        left: "center"
      },
      tooltip: { trigger: "axis" },
      grid: { top: 50, right: 20, bottom: 30, left: 40 },
      xAxis: { type: "category", data: conversationData.labels },
      yAxis: { type: "value" },
      series: conversationData.datasets.map(dataset => ({
        name: dataset.label,
        type: "bar",
        data: dataset.data,
        itemStyle: { color: dataset.borderColor, borderRadius: [4, 4, 0, 0] }
      }))
    } as EChartsOption);
  }

  if (knowledgeChartRef.value) {
    knowledgeChart = echarts.init(knowledgeChartRef.value);
    knowledgeChart.setOption({
      tooltip: { trigger: "item" },
      series: [
        {
          name: "File Types",
          type: "pie",
          radius: "70%",
          center: ["50%", "55%"],
          data: [
            { value: data.stats.knowledge?.fileTypes.pdf, name: "PDF" },
            { value: data.stats.knowledge?.fileTypes.docx, name: "DOCX" },
            { value: data.stats.knowledge?.fileTypes.txt, name: "TXT" },
            { value: data.stats.knowledge?.fileTypes.other, name: "Other" }
          ]
        }
      ]
    } as EChartsOption);
  }

  if (storageChartRef.value) {
    storageChart = echarts.init(storageChartRef.value);
    storageChart.setOption({
      tooltip: { trigger: "item" },
      series: [
        {
          name: "Storage",
          type: "pie",
          radius: ["40%", "70%"],
          center: ["50%", "55%"],
          data: [
            { value: data.stats.storage?.documentsSizeMB, name: "Documents" },
            {
              value: data.stats.storage?.attachmentsSizeMB,
              name: "Attachments"
            },
            { value: data.stats.storage?.remainingQuotaMB, name: "Available" }
          ],
          label: { show: false },
          labelLine: { show: false }
        }
      ]
    } as EChartsOption);
  }
};

const resizeCharts = () => {
  tokenChart?.resize();
  conversationChart?.resize();
  knowledgeChart?.resize();
  storageChart?.resize();
};

// --- LIFECYCLE HOOKS ---
onMounted(() => {
  loadDashboardData();
  window.addEventListener("resize", resizeCharts);
});

onUnmounted(() => {
  window.removeEventListener("resize", resizeCharts);
});
</script>

<template>
  <div
    v-if="loading"
    class="flex justify-center items-center h-screen text-2xl text-gray-500 font-inter"
  >
    Đang tải dữ liệu Dashboard...
  </div>
  <div v-else class="min-h-screen bg-slate-50">
    <!-- Hero Header Section -->
    <div
      class="relative bg-gradient-to-br from-indigo-500 via-purple-600 to-purple-700 px-8 pt-12 pb-16 overflow-hidden"
    >
      <!-- Background decorations -->
      <div class="absolute inset-0 opacity-60">
        <div
          class="absolute top-0 left-0 w-96 h-96 bg-white/10 rounded-full -translate-x-1/2 -translate-y-1/2"
        />
        <div
          class="absolute top-20 right-0 w-64 h-64 bg-white/10 rounded-full translate-x-1/2 -translate-y-1/2"
        />
      </div>

      <div
        class="relative z-10 w-full flex flex-col lg:flex-row justify-between items-start lg:items-center gap-8"
      >
        <div class="text-center lg:text-left">
          <div
            class="inline-flex items-center gap-2 bg-white/15 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 text-white text-sm font-medium mb-6"
          >
            <span class="text-base">📊</span>
            <span>Analytics Dashboard</span>
          </div>
          <h1
            class="text-4xl lg:text-6xl font-extrabold text-white leading-tight mb-4"
          >
            Chatbot Performance
            <span
              class="bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent"
              >Insights</span
            >
          </h1>
          <p class="text-xl text-white/80 leading-relaxed max-w-lg">
            Comprehensive analytics and performance metrics for your AI chatbots
          </p>
        </div>

        <div
          class="flex flex-col gap-6 items-center lg:items-end w-full lg:w-auto"
        >
          <div class="flex flex-col gap-2">
            <label class="text-white/80 text-sm font-medium">Time Period</label>
            <el-radio-group
              v-model="timeFilter"
              class="modern-radio-group"
              @change="handleTimeFilterChange"
            >
              <el-radio-button value="7d">7 Days</el-radio-button>
              <el-radio-button value="30d">30 Days</el-radio-button>
              <el-radio-button value="3m">3 Months</el-radio-button>
            </el-radio-group>
          </div>
          <el-button
            type="primary"
            class="export-button bg-gradient-to-r from-yellow-300 to-orange-400 border-none text-gray-800 font-semibold px-6 py-3 rounded-xl hover:-translate-y-0.5 hover:shadow-lg transition-all duration-300"
            @click="exportData"
          >
            Export Report
          </el-button>
        </div>
      </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="relative z-20 -mt-16 px-8 mx-auto">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Primary Metric - Featured -->
        <div
          class="bg-gradient-to-br from-indigo-500 to-purple-600 text-white rounded-3xl p-6 shadow-xl hover:-translate-y-1 transition-all duration-300"
        >
          <div class="flex justify-between items-start mb-4">
            <div
              class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center"
            >
              <span class="text-2xl">🤖</span>
            </div>
            <div
              class="flex items-center gap-1 bg-white/20 rounded-lg px-2 py-1 text-xs font-semibold"
            >
              <span>▲</span>
              <span>+12%</span>
            </div>
          </div>
          <div>
            <h3 class="text-4xl font-extrabold mb-2">
              {{ data.stats.chatbot?.total }}
            </h3>
            <p class="text-lg font-semibold mb-4">Active Chatbots</p>
            <div class="flex items-center gap-4">
              <div class="text-center">
                <div class="text-xl font-bold">
                  {{ data.stats.chatbot?.active }}
                </div>
                <div class="text-xs uppercase tracking-wide opacity-80">
                  Active
                </div>
              </div>
              <div class="w-px h-8 bg-white/30" />
              <div class="text-center">
                <div class="text-xl font-bold">
                  {{ data.stats.chatbot?.draft }}
                </div>
                <div class="text-xs uppercase tracking-wide opacity-80">
                  Draft
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Secondary Metrics -->
        <div
          class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200 hover:-translate-y-1 hover:shadow-xl transition-all duration-300"
        >
          <div class="flex justify-between items-start mb-4">
            <div
              class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center"
            >
              <span class="text-2xl text-green-600">💬</span>
            </div>
            <div
              class="flex items-center gap-1 bg-green-100 text-green-600 rounded-lg px-2 py-1 text-xs font-semibold"
            >
              <span>▲</span><span>+8%</span>
            </div>
          </div>
          <div>
            <h3 class="text-3xl font-extrabold text-gray-900 mb-2">
              {{ data.stats.conversation?.total.toLocaleString() }}
            </h3>
            <p class="text-base font-semibold text-gray-600 mb-4">
              Conversations
            </p>
            <div class="text-sm text-gray-500">
              {{ data.stats.conversation?.today }} today
            </div>
          </div>
        </div>

        <div
          class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200 hover:-translate-y-1 hover:shadow-xl transition-all duration-300"
        >
          <div class="flex justify-between items-start mb-4">
            <div
              class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center"
            >
              <span class="text-2xl text-yellow-600">⚙️</span>
            </div>
            <div
              class="flex items-center gap-1 bg-gray-100 text-gray-600 rounded-lg px-2 py-1 text-xs font-semibold"
            >
              <span>-</span><span>0%</span>
            </div>
          </div>
          <div>
            <h3 class="text-3xl font-extrabold text-gray-900 mb-2">
              {{ data.stats.token?.total.toLocaleString() }}
            </h3>
            <p class="text-base font-semibold text-gray-600 mb-4">
              Tokens Used
            </p>
            <div class="text-sm text-gray-500">
              ${{ data.stats.token?.estimatedCost }} cost
            </div>
          </div>
        </div>

        <div
          class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200 hover:-translate-y-1 hover:shadow-xl transition-all duration-300"
        >
          <div class="flex justify-between items-start mb-4">
            <div
              class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center"
            >
              <span class="text-2xl text-blue-600">💾</span>
            </div>
            <div
              class="flex items-center gap-1 bg-green-100 text-green-600 rounded-lg px-2 py-1 text-xs font-semibold"
            >
              <span>▲</span><span>+5%</span>
            </div>
          </div>
          <div>
            <h3 class="text-3xl font-extrabold text-gray-900 mb-2">
              {{ storageUsedFormatted }}
            </h3>
            <p class="text-base font-semibold text-gray-600 mb-4">
              Storage Used
            </p>
            <div class="text-sm text-gray-500">
              {{ data.stats.knowledge?.totalDocuments }} documents
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Analytics Charts -->
    <div class="px-8 pt-5 mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200">
          <div ref="tokenChartRef" class="h-80 w-full" />
        </div>
        <div class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200">
          <div ref="conversationChartRef" class="h-80 w-full" />
        </div>
      </div>
    </div>

    <!-- Data Insights Section -->
    <div class="px-8 py-5 mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Knowledge Base Chart -->
        <div class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200">
          <div class="flex justify-between items-start mb-6">
            <div>
              <h3 class="text-xl font-bold text-gray-900 mb-1">
                Knowledge Base
              </h3>
              <p class="text-sm text-gray-600">Document type distribution</p>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-gray-900">
                {{ data.stats.knowledge?.totalDocuments }}
              </div>
              <div class="text-xs uppercase tracking-wide text-gray-500">
                Documents
              </div>
            </div>
          </div>
          <div>
            <div ref="knowledgeChartRef" class="h-64 w-full mb-4" />
            <div class="bg-slate-50 rounded-xl p-3 flex items-center gap-2">
              <span class="text-yellow-400 text-base">⭐</span>
              <span class="text-sm text-gray-600 font-medium"
                >Most Queried:</span
              >
              <span class="text-sm text-gray-900 font-semibold">
                {{ data.stats.knowledge?.mostQueriedDoc }}
              </span>
            </div>
          </div>
        </div>

        <!-- Storage Usage Chart -->
        <div class="bg-white rounded-3xl p-6 shadow-lg border border-slate-200">
          <div class="flex justify-between items-start mb-6">
            <div>
              <h3 class="text-xl font-bold text-gray-900 mb-1">
                Storage Usage
              </h3>
              <p class="text-sm text-gray-600">Space utilization breakdown</p>
            </div>
            <div>
              <div
                class="px-3 py-1 rounded-xl text-xs font-semibold"
                :class="
                  storageUsagePercentage > 80
                    ? 'bg-red-100 text-red-600'
                    : 'bg-green-100 text-green-600'
                "
              >
                {{ storageUsagePercentage }}% Used
              </div>
            </div>
          </div>
          <div>
            <div ref="storageChartRef" class="h-64 w-full mb-4" />
            <div>
              <div class="flex justify-between items-center mb-2">
                <div class="text-base font-semibold">
                  {{ storageUsedFormatted }}
                  <span class="text-sm text-gray-500 font-normal"
                    >/ {{ storageQuotaFormatted }}</span
                  >
                </div>
              </div>
              <div class="h-2 bg-slate-100 rounded-full overflow-hidden mb-2">
                <div
                  class="h-full rounded-full transition-all duration-500"
                  :style="{ width: storageUsagePercentage + '%' }"
                  :class="
                    storageUsagePercentage > 80
                      ? 'bg-gradient-to-r from-red-600 to-red-400'
                      : 'bg-gradient-to-r from-green-600 to-green-400'
                  "
                />
              </div>
              <div class="text-xs text-gray-500 text-center">
                {{ storageRemainingFormatted }} remaining
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* Element Plus Overrides for modern radio group */
:deep(.modern-radio-group) {
  .el-radio-button__inner {
    border-radius: 8px !important;
    margin: 0 2px;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
  }
  .el-radio-button__inner:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: white;
  }
  .el-radio-button:first-child .el-radio-button__inner,
  .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 8px !important;
  }
  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    background: white !important;
    border-color: white !important;
    color: #667eea !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    font-weight: 600;
  }
}
</style>
