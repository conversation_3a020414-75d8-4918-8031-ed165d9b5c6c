// Local icons are stored here, loaded in src/layout/index.vue file to avoid loading on first startup
import { addIcon } from "@iconify/vue/dist/offline";

// Local menu icons, backend returns corresponding icon strings in route's icon field and frontend uses addIcon here to render menu icons
// @iconify-icons/ep
import Menu from "@iconify-icons/ep/menu";
import Edit from "@iconify-icons/ep/edit";
import SetUp from "@iconify-icons/ep/set-up";
import Guide from "@iconify-icons/ep/guide";
import Monitor from "@iconify-icons/ep/monitor";
import Lollipop from "@iconify-icons/ep/lollipop";
import Histogram from "@iconify-icons/ep/histogram";
import HomeFilled from "@iconify-icons/ep/home-filled";
import Close from "@iconify-icons/ep/close";
import RefreshRight from "@iconify-icons/ep/refresh-right";

addIcon("ep:menu", Menu);
addIcon("ep:edit", Edit);
addIcon("ep:set-up", SetUp);
addIcon("ep:guide", Guide);
addIcon("ep:monitor", Monitor);
addIcon("ep:lollipop", Lollipop);
addIcon("ep:histogram", Histogram);
addIcon("ep:home-filled", HomeFilled);
addIcon("ep:close", Close);
addIcon("ep:refresh-right", RefreshRight);
// @iconify-icons/ri
import Tag from "@iconify-icons/ri/bookmark-2-line";
import Ppt from "@iconify-icons/ri/file-ppt-2-line";
import Card from "@iconify-icons/ri/bank-card-line";
import Role from "@iconify-icons/ri/admin-fill";
import Info from "@iconify-icons/ri/file-info-line";
import Dept from "@iconify-icons/ri/git-branch-line";
import Table from "@iconify-icons/ri/table-line";
import Links from "@iconify-icons/ri/links-fill";
import Search from "@iconify-icons/ri/search-line";
import FlUser from "@iconify-icons/ri/admin-line";
import Setting from "@iconify-icons/ri/settings-3-line";
import MindMap from "@iconify-icons/ri/mind-map";
import BarChart from "@iconify-icons/ri/bar-chart-horizontal-line";
import LoginLog from "@iconify-icons/ri/window-line";
import Artboard from "@iconify-icons/ri/artboard-line";
import SystemLog from "@iconify-icons/ri/file-search-line";
import ListCheck from "@iconify-icons/ri/list-check";
import UbuntuFill from "@iconify-icons/ri/ubuntu-fill";
import OnlineUser from "@iconify-icons/ri/user-voice-line";
import EditBoxLine from "@iconify-icons/ri/edit-box-line";
import OperationLog from "@iconify-icons/ri/history-fill";
import InformationLine from "@iconify-icons/ri/information-line";
import TerminalWindowLine from "@iconify-icons/ri/terminal-window-line";
import CheckboxCircleLine from "@iconify-icons/ri/checkbox-circle-line";
// Additional icons for router modules
import RobotLine from "@iconify-icons/ri/robot-2-line";
import ChatLine from "@iconify-icons/ri/chat-3-line";
import MessageLine from "@iconify-icons/ri/message-3-line";
import FileTextLine from "@iconify-icons/ri/file-text-line";
import UserLine from "@iconify-icons/ri/user-line";
import NotificationLine from "@iconify-icons/ri/notification-line";
import GlobalLine from "@iconify-icons/ri/global-line";
import DashboardLine from "@iconify-icons/ri/dashboard-line";
import BuildingLine from "@iconify-icons/ri/building-line";
import BookLine from "@iconify-icons/ri/book-line";
import RobotLineSingle from "@iconify-icons/ri/robot-line";
// Common UI icons
import AddCircleLine from "@iconify-icons/ri/add-circle-line";
import DeleteBinLine from "@iconify-icons/ri/delete-bin-line";
import DeleteBin2Line from "@iconify-icons/ri/delete-bin-2-line";
import RefreshLine from "@iconify-icons/ri/refresh-line";
import ArrowDownSLine from "@iconify-icons/ri/arrow-down-s-line";
import ArrowLeftLine from "@iconify-icons/ri/arrow-left-line";
import LoaderLine from "@iconify-icons/ri/loader-4-line";
import ErrorWarningLine from "@iconify-icons/ri/error-warning-line";
import ShieldUserLine from "@iconify-icons/ri/shield-user-line";
import KeyLine from "@iconify-icons/ri/key-line";
import EditLine from "@iconify-icons/ri/edit-line";
import LoginCircleLine from "@iconify-icons/ri/login-circle-line";
import UserAddLine from "@iconify-icons/ri/user-add-line";
// Tag-related icons
import FullscreenFill from "@iconify-icons/ri/fullscreen-fill";
import SubtractLine from "@iconify-icons/ri/subtract-line";
import TextSpacing from "@iconify-icons/ri/text-spacing";
import TextDirectionL from "@iconify-icons/ri/text-direction-l";
import TextDirectionR from "@iconify-icons/ri/text-direction-r";
import FullscreenExitFill from "@iconify-icons/ri/fullscreen-exit-fill";
import ArrowRightSLine from "@iconify-icons/ri/arrow-right-s-line";
import ArrowLeftSLine from "@iconify-icons/ri/arrow-left-s-line";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";

addIcon("ri:bookmark-2-line", Tag);
addIcon("ri:file-ppt-2-line", Ppt);
addIcon("ri:bank-card-line", Card);
addIcon("ri:admin-fill", Role);
addIcon("ri:file-info-line", Info);
addIcon("ri:git-branch-line", Dept);
addIcon("ri:links-fill", Links);
addIcon("ri:table-line", Table);
addIcon("ri:search-line", Search);
addIcon("ri:admin-line", FlUser);
addIcon("ri:settings-3-line", Setting);
addIcon("ri:mind-map", MindMap);
addIcon("ri:bar-chart-horizontal-line", BarChart);
addIcon("ri:window-line", LoginLog);
addIcon("ri:file-search-line", SystemLog);
addIcon("ri:artboard-line", Artboard);
addIcon("ri:list-check", ListCheck);
addIcon("ri:ubuntu-fill", UbuntuFill);
addIcon("ri:user-voice-line", OnlineUser);
addIcon("ri:edit-box-line", EditBoxLine);
addIcon("ri:history-fill", OperationLog);
addIcon("ri:information-line", InformationLine);
addIcon("ri:terminal-window-line", TerminalWindowLine);
addIcon("ri:checkbox-circle-line", CheckboxCircleLine);
// Register additional router module icons
addIcon("ri:robot-2-line", RobotLine);
addIcon("ri:chat-3-line", ChatLine);
addIcon("ri:message-3-line", MessageLine);
addIcon("ri:file-text-line", FileTextLine);
addIcon("ri:user-line", UserLine);
addIcon("ri:notification-line", NotificationLine);
addIcon("ri:global-line", GlobalLine);
addIcon("ri:dashboard-line", DashboardLine);
addIcon("ri:building-line", BuildingLine);
addIcon("ri:book-line", BookLine);
addIcon("ri:robot-line", RobotLineSingle);
// Register common UI icons
addIcon("ri:add-circle-line", AddCircleLine);
addIcon("ri:delete-bin-line", DeleteBinLine);
addIcon("ri:delete-bin-2-line", DeleteBin2Line);
addIcon("ri:refresh-line", RefreshLine);
addIcon("ri:arrow-down-s-line", ArrowDownSLine);
addIcon("ri:arrow-left-line", ArrowLeftLine);
addIcon("ri:loader-4-line", LoaderLine);
addIcon("ri:error-warning-line", ErrorWarningLine);
addIcon("ri:shield-user-line", ShieldUserLine);
addIcon("ri:key-line", KeyLine);
addIcon("ri:edit-line", EditLine);
addIcon("ri:login-circle-line", LoginCircleLine);
addIcon("ri:user-add-line", UserAddLine);
// Register tag-related icons
addIcon("ri:fullscreen-fill", FullscreenFill);
addIcon("ri:subtract-line", SubtractLine);
addIcon("ri:text-spacing", TextSpacing);
addIcon("ri:text-direction-l", TextDirectionL);
addIcon("ri:text-direction-r", TextDirectionR);
addIcon("ri:fullscreen-exit-fill", FullscreenExitFill);
addIcon("ri:arrow-right-s-line", ArrowRightSLine);
addIcon("ri:arrow-left-s-line", ArrowLeftSLine);
addIcon("ri:logout-circle-r-line", LogoutCircleRLine);
