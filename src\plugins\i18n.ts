// src/plugins/i18n.ts

import { createI18n } from "vue-i18n";
import { storageLocal } from "@pureadmin/utils";

import en from "../../locales/en.json";
import vi from "../../locales/vi.json";

const getInitialLocale = (): string => {
  if (typeof window !== "undefined") {
    const storage = storageLocal();
    return storage.getItem("locale") || "vi";
  }
  return "vi";
};

const i18n = createI18n({
  legacy: false,
  // Use safe function to get language
  locale: getInitialLocale(),
  fallbackLocale: "en",
  messages: {
    en,
    vi
  }
});

export default i18n;

export const $t = i18n.global.t;
