<template>
  <div class="tiny-editor">
    <el-input
      v-model="content"
      type="textarea"
      :rows="rows"
      :placeholder="placeholder"
      @input="handleInput"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface Props {
  modelValue?: string;
  placeholder?: string;
  rows?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "Please enter content...",
  rows: 6
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
}>();

const content = ref(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    content.value = newValue;
  }
);

const handleInput = (value: string) => {
  emit("update:modelValue", value);
};
</script>

<style scoped>
.tiny-editor {
  width: 100%;
}
</style>
