import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag, ElSwitch } from "element-plus";
import { h } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

// Helper function for date formatting
const formatDateTime = (date: string | null): string => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
};

// Menu type mapping
const menuTypeMap = {
  0: { label: "Menu", type: "primary" },
  1: { label: "Iframe", type: "warning" },
  2: { label: "External Link", type: "danger" },
  3: { label: "Button", type: "info" }
} as const;

export const columns: TableColumnList = [
  {
    type: "selection",
    width: 55,
    align: "center",
    hide: false
  },
  {
    headerRenderer: () => $t("Title"),
    prop: "title",
    align: "left",
    minWidth: 200,
    cellRenderer: ({ row }) => (
      <>
        {row.icon && (
          <span class="inline-block mr-2">
            {h(useRenderIcon(row.icon), {
              style: { paddingTop: "1px" }
            })}
          </span>
        )}
        <span>{$t(row.title)}</span>
      </>
    )
  },
  {
    headerRenderer: () => $t("Name"),
    prop: "name",
    align: "left",
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    headerRenderer: () => $t("Path"),
    prop: "path",
    align: "left",
    minWidth: 180,
    showOverflowTooltip: true,
    formatter: ({ path }) => path || "-"
  },
  {
    headerRenderer: () => $t("Type"),
    prop: "menuType",
    align: "center",
    width: 100,
    cellRenderer: ({ row }) => {
      const { label, type } = menuTypeMap[row.menuType] || menuTypeMap[0];
      return h(
        ElTag,
        {
          type,
          size: "small",
          effect: "plain"
        },
        () => $t(label)
      );
    }
  },
  {
    headerRenderer: () => $t("Parent"),
    prop: "parentId",
    align: "center",
    width: 100,
    formatter: ({ parentId }) => parentId || "-"
  },
  {
    headerRenderer: () => $t("Order"),
    prop: "rank",
    align: "center",
    width: 80,
    sortable: true
  },
  {
    headerRenderer: () => $t("Show Link"),
    prop: "showLink",
    align: "center",
    width: 100,
    cellRenderer: ({ row }) => {
      return h(ElSwitch, {
        modelValue: row.showLink,
        disabled: true,
        size: "small"
      });
    }
  },
  {
    headerRenderer: () => $t("Keep Alive"),
    prop: "keepAlive",
    align: "center",
    width: 100,
    cellRenderer: ({ row }) => {
      return h(ElSwitch, {
        modelValue: row.keepAlive,
        disabled: true,
        size: "small"
      });
    }
  },
  {
    headerRenderer: () => $t("Created At"),
    prop: "createdAt",
    align: "center",
    width: 160,
    sortable: true,
    formatter: ({ createdAt }) => formatDateTime(createdAt)
  },
  {
    headerRenderer: () => $t("Updated At"),
    prop: "updatedAt",
    align: "center",
    width: 160,
    sortable: true,
    formatter: ({ updatedAt }) => formatDateTime(updatedAt)
  },
  {
    headerRenderer: () => $t("Operation"),
    fixed: "right",
    width: 160,
    slot: "operation",
    sortable: false,
    align: "center"
  }
];
