import { http } from "@/utils/http";

// Types based on API documentation
export interface DashboardStats {
  chatbot: {
    total: number;
    active: number;
    draft: number;
    mostUsedBot: string;
  };
  conversation: {
    total: number;
    today: number;
    week: number;
    avgConversationLength: number;
  };
  token: {
    total: number;
    estimatedCost: number;
  };
  knowledge: {
    totalDocuments: number;
    fileTypes: {
      pdf: number;
      docx: number;
      txt: number;
      other: number;
    };
    mostQueriedDoc: string;
  };
  storage: {
    totalUsedMB: number;
    documentsSizeMB: number;
    attachmentsSizeMB: number;
    remainingQuotaMB: number;
    quotaLimitMB: number;
    usagePercent?: number;
  };
}

export interface ChartDataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
  tension: number;
}

export interface DashboardCharts {
  tokenTrend: {
    labels: string[];
    datasets: ChartDataset[];
  };
  conversationTrend: {
    labels: string[];
    datasets: ChartDataset[];
  };
}

export interface DashboardResponse {
  success: boolean;
  message: string;
  data: {
    stats: DashboardStats;
    charts: DashboardCharts;
    metadata?: {
      lastUpdated: string;
      timezone: string;
      currency: string;
    };
  };
  cached?: boolean;
  cache_expires_at?: string;
}

export interface DashboardSummaryResponse {
  success: boolean;
  message: string;
  data: {
    totalBots: number;
    totalConversations: number;
    totalTokens: number;
    totalDocuments: number;
    storageUsedPercent: number;
  };
}

// API Service Class
export class DashboardAPI {
  private static baseURL = "/api/v1/auth/dashboard";

  /**
   * Get complete dashboard data
   * @param period - Time period: day|week|month|year
   * @param options - Additional options
   */
  static async getDashboardData(
    period: string = "week",
    options: {
      timezone?: string;
      include_charts?: boolean;
      include_stats?: boolean;
    } = {}
  ): Promise<DashboardResponse> {
    return http.request<DashboardResponse>("get", `${this.baseURL}/data`, {
      params: {
        period,
        ...options
      }
    });
  }

  /**
   * Get statistics only (no charts)
   */
  static async getStats(
    period: string = "week"
  ): Promise<{ success: boolean; data: DashboardStats }> {
    return http.request("get", `${this.baseURL}/stats`, {
      params: { period }
    });
  }

  /**
   * Get charts only (no stats)
   */
  static async getCharts(
    period: string = "week"
  ): Promise<{ success: boolean; data: DashboardCharts }> {
    return http.request("get", `${this.baseURL}/charts`, {
      params: { period }
    });
  }

  /**
   * Get dashboard summary
   */
  static async getSummary(): Promise<DashboardSummaryResponse> {
    return http.request<DashboardSummaryResponse>(
      "get",
      `${this.baseURL}/summary`
    );
  }
}

// Helper function to map time filter to API period
export const mapTimeFilterToPeriod = (filter: string): string => {
  const mapping: Record<string, string> = {
    "7d": "week",
    "30d": "month",
    "3m": "quarter"
  };
  return mapping[filter] || "week";
};
