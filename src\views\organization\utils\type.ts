export type FormItemProps = {
  id?: number | null;
  name?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone?: string;
  logo?: string | File;
  logoUrl?: string;
  status?: "active" | "inactive" | "suspended";
  organizationType?: "company" | "nonprofit" | "government" | "educational";
  industry?: string;
  employeeCount?: number;
  foundedYear?: number;
  isVerified?: boolean;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
};

export type OrganizationFilterProps = {
  name?: string;
  email?: string;
  status?: "active" | "inactive" | "suspended";
  organizationType?: "company" | "nonprofit" | "government" | "educational";
  industry?: string;
  country?: string;
  isVerified?: boolean;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
