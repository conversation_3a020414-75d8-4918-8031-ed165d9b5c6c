import { removeToken, setToken, type DataInfo } from "./auth";
import { subBefore, getQueryMap } from "@pureadmin/utils";

/**
 * Simple frontend single sign-on login，Write according to actual business needs，After platform startup, you can jump to this link for local testing http://localhost:8848/#/permission/page/index?username=sso&roles=admin&accessToken=eyJhbGciOiJIUzUxMiJ9.admin
 * 划重点：
 * CheckWhether it is单点Login，不为则直接Back不再进Row任何逻辑处理，下面是单点Login后的逻辑处理
 * 1.Clear本地旧Info；
 * 2.Get important parameter info from url，Then save locally through setToken；
 * 3.Delete不需要显示在 url 的Parameter
 * 4.Use window.location.replace to jump to correct page
 */
(function () {
  // 获取 url 中的Parameter
  const params = getQueryMap(location.href) as DataInfo<Date>;
  const must = ["username", "roles", "accessToken"];
  const mustLength = must.length;
  if (Object.keys(params).length !== mustLength) return;

  // Only when url parameters satisfy all values in must, it is determined as single sign-on login, to avoid infinite loop when refreshing page during non-single sign-on login
  let sso = [];
  let start = 0;

  while (start < mustLength) {
    if (Object.keys(params).includes(must[start]) && sso.length <= mustLength) {
      sso.push(must[start]);
    } else {
      sso = [];
    }
    start++;
  }

  if (sso.length === mustLength) {
    // 判定为单点Login

    // Clear本地旧Info
    removeToken();

    // Save新Info到本地
    setToken(params);

    // Delete不需要显示在 url 的Parameter
    delete params.roles;
    delete params.accessToken;

    const newUrl = `${location.origin}${location.pathname}${subBefore(
      location.hash,
      "?"
    )}?${JSON.stringify(params)
      .replace(/["{}]/g, "")
      .replace(/:/g, "=")
      .replace(/,/g, "&")}`;

    // 替换历史记录项
    window.location.replace(newUrl);
  } else {
    return;
  }
})();
