const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/auth/login/index.vue"),
    meta: {
      title: "Login",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("@/views/auth/register/index.vue"),
    meta: {
      title: "Register",
      showLink: false,
      rank: 102
    }
  },
  {
    path: "/forget",
    name: "ForgetPassword",
    component: () => import("@/views/auth/forget/index.vue"),
    meta: {
      title: "忘记Password",
      showLink: false,
      rank: 103
    }
  },
  {
    path: "/reset-password",
    name: "ResetPassword",
    component: () => import("@/views/auth/reset-password/index.vue"),
    meta: {
      title: "ResetPassword",
      showLink: false,
      rank: 104
    }
  },
  {
    path: "/verify-email",
    name: "VerifyEmail",
    component: () => import("@/views/auth/verify-email/index.vue"),
    meta: {
      title: "验证Email",
      showLink: false,
      rank: 105
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "Load中...",
      showLink: false,
      rank: 106
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;
