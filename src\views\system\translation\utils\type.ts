// <PERSON><PERSON>\Translatable structure
export type Translation = {
  id: number;
  key: string;
  group: string;
  text: Record<string, string>; // { "en": "Hello", "vi": "Xin chào" }
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type TranslationItem = {
  id: number;
  key: string;
  group: string;
  locale: string;
  value: string;
  createdAt: string;
  updatedAt: string;
};

export type FormItemProps = {
  id?: number | null;
  key?: string;
  group?: string;
  translations?: Record<string, string>; // { "en": "Hello", "vi": "Xin chào" }
  [key: string]: any;
};

export type TranslationFilterProps = {
  key?: string;
  group?: string;
  locale?: string;
  value?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};

export type AvailableLocale = {
  code: string;
  name: string;
  flag?: string;
};
