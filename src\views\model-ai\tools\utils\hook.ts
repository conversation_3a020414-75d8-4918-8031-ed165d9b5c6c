import { reactive, ref } from "vue";
import {
  getModelTools,
  createModelTool,
  updateModelToolById,
  deleteModelToolById,
  bulkDeleteModelTools,
  bulkDestroyModelTools,
  destroyModelToolById,
  restoreModelToolById,
  bulkRestoreModelTools,
  testModelTool,
  validateModelTool
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { ModelToolFilterProps } from "@/views/model-ai/tools/utils/type";

export function useModelToolHook() {
  // Data/State
  const loading = ref(false);
  const filterRef = ref<ModelToolFilterProps>({});
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "name", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({ status: "active", isActive: true });
  const modelToolFormRef = ref();
  const testDialogVisible = ref(false);
  const testLoading = ref(false);
  const testResult = ref("");

  // API Handlers
  const fnGetModelTools = async () => {
    loading.value = true;
    try {
      const response = await getModelTools({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      console.error("Get Model Tools error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  const fnHandleCreateModelTool = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createModelTool(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetModelTools();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateModelTool = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateModelToolById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetModelTools();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Test Tool
  const fnTestModelTool = async (id: number, parameters: any) => {
    try {
      testLoading.value = true;
      const response = await testModelTool(id, { parameters });
      if (response.success) {
        testResult.value = JSON.stringify(response.data, null, 2);
        message($t("Test successful"), { type: "success" });
      } else {
        message(response.message || $t("Test failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Test failed"),
        { type: "error" }
      );
    } finally {
      testLoading.value = false;
    }
  };

  // Validate Tool
  const fnValidateModelTool = async (id: number) => {
    try {
      loading.value = true;
      const response = await validateModelTool(id);
      if (response.success) {
        message(response.message || $t("Validation successful"), {
          type: "success"
        });
      } else {
        message(response.message || $t("Validation failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Validation failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetModelTools();
  };

  const fnHandlePageChange = (val: number) => {
    pagination.currentPage = val;
    fnGetModelTools();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetModelTools();
  };

  // Delete handlers
  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteModelToolById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetModelTools();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteModelTools({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetModelTools();
      } else {
        message(response.message || $t("Bulk delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Bulk delete failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  // UI Action Handlers
  const handleDelete = (id: number) => {
    ElMessageBox.confirm(
      $t("Are you sure to delete this item?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    ).then(() => {
      if (filterRef.value.isTrashed === "yes") {
        fnHandleDelete(id);
      } else {
        handleDestroy(id);
      }
    });
  };

  const handleBulkDelete = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    ElMessageBox.confirm(
      $t("Are you sure to delete selected items?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    ).then(() => {
      const ids = multipleSelection.value.map(item => item.id);
      if (filterRef.value.isTrashed === "yes") {
        fnHandleBulkDelete(ids);
      } else {
        handleBulkDestroy();
      }
    });
  };

  const handleDestroy = async (id: number) => {
    try {
      loading.value = true;
      const response = await destroyModelToolById(id);
      if (response.success) {
        message(response.message || $t("Move to trash successful"), {
          type: "success"
        });
        await fnGetModelTools();
      } else {
        message(response.message || $t("Move to trash failed"), {
          type: "error"
        });
      }
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Move to trash failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDestroy = async () => {
    try {
      loading.value = true;
      const ids = multipleSelection.value.map(item => item.id);
      const response = await bulkDestroyModelTools({ ids });
      if (response.success) {
        message(response.message || $t("Bulk move to trash successful"), {
          type: "success"
        });
        await fnGetModelTools();
      } else {
        message(response.message || $t("Bulk move to trash failed"), {
          type: "error"
        });
      }
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Bulk move to trash failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  const handleRestore = async (id: number) => {
    try {
      loading.value = true;
      const response = await restoreModelToolById(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetModelTools();
      } else {
        message(response.message || $t("Restore failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Restore failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  const handleBulkRestore = async () => {
    try {
      loading.value = true;
      const ids = multipleSelection.value.map(item => item.id);
      const response = await bulkRestoreModelTools({ ids });
      if (response.success) {
        message(response.message || $t("Bulk restore successful"), {
          type: "success"
        });
        await fnGetModelTools();
      } else {
        message(response.message || $t("Bulk restore failed"), {
          type: "error"
        });
      }
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Bulk restore failed"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  // Form Handlers
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdateModelTool(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateModelTool(values);
    if (success) {
      drawerValues.value = { status: "active", isActive: true };
      modelToolFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: ModelToolFilterProps) => {
    filterRef.value = values;
    await fnGetModelTools();
  };

  const handleTest = (tool: any) => {
    drawerValues.value = tool;
    testDialogVisible.value = true;
    testResult.value = "";
  };

  const handleValidate = async (id: number) => {
    await fnValidateModelTool(id);
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    modelToolFormRef,
    testDialogVisible,
    testLoading,
    testResult,

    // API Handlers
    fnGetModelTools,
    fnHandleCreateModelTool,
    fnHandleUpdateModelTool,
    fnHandleDelete,
    fnHandleBulkDelete,
    fnTestModelTool,
    fnValidateModelTool,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,
    handleTest,
    handleValidate,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
