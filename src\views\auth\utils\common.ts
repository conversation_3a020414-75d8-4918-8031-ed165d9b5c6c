import { ref, readonly } from "vue";
import { $t } from "@/plugins/i18n";

// Common validation patterns
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const USERNAME_REGEX = /^[a-zA-Z0-9_]+$/;
export const PHONE_REGEX = /^[+]?[\d\s\-()]+$/;

// Password strength checker
export interface PasswordStrength {
  score: number; // 0-4
  feedback: string[];
  isValid: boolean;
}

export const checkPasswordStrength = (
  password: string,
  requirements: any = {}
): PasswordStrength => {
  const {
    minLength = 6,
    requireNumbers = false,
    requireSymbols = false,
    requireUppercase = false,
    requireLowercase = false
  } = requirements;

  let score = 0;
  const feedback: string[] = [];

  // Length check
  if (password.length >= minLength) {
    score += 1;
  } else {
    feedback.push(
      $t("Password must be at least :length characters", { length: minLength })
    );
  }

  // Numbers check
  if (/\d/.test(password)) {
    score += 1;
  } else if (requireNumbers) {
    feedback.push($t("Password must contain at least one number"));
  }

  // Symbols check
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1;
  } else if (requireSymbols) {
    feedback.push($t("Password must contain at least one symbol"));
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else if (requireUppercase) {
    feedback.push($t("Password must contain at least one uppercase letter"));
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else if (requireLowercase) {
    feedback.push($t("Password must contain at least one lowercase letter"));
  }

  // Additional complexity checks
  if (password.length >= 12) score += 1;
  if (
    /[!@#$%^&*(),.?":{}|<>]/.test(password) &&
    /\d/.test(password) &&
    /[A-Z]/.test(password) &&
    /[a-z]/.test(password)
  ) {
    score += 1;
  }

  const isValid = feedback.length === 0;

  return {
    score: Math.min(score, 4),
    feedback,
    isValid
  };
};

// Format validation error messages
export const formatValidationError = (
  field: string,
  rule: string,
  value?: any
): string => {
  const messages: Record<string, string> = {
    required: $t(":field is required", { field }),
    email: $t("Please enter a valid email address"),
    minLength: $t(":field must be at least :value characters", {
      field,
      value
    }),
    maxLength: $t(":field must be less than :value characters", {
      field,
      value
    }),
    pattern: $t(":field format is invalid", { field }),
    match: $t(":field does not match", { field })
  };

  return messages[rule] || $t("Invalid :field", { field });
};

// Social login helpers
export const getSocialLoginUrl = (
  provider: string,
  redirectUrl?: string
): string => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || "";
  const params = new URLSearchParams();

  if (redirectUrl) {
    params.append("redirect_url", redirectUrl);
  }

  const queryString = params.toString();
  return `${baseUrl}/api/auth/social/${provider}${queryString ? `?${queryString}` : ""}`;
};

// OTP helpers
export const formatOtpInput = (value: string): string => {
  return value.replace(/\D/g, "").slice(0, 6);
};

export const isValidOtp = (otp: string): boolean => {
  return /^\d{6}$/.test(otp);
};

// Form helpers
export const clearFormErrors = (formRef: any) => {
  if (formRef?.clearValidate) {
    formRef.clearValidate();
  }
};

export const resetForm = (formRef: any, formData: any) => {
  if (formRef?.resetFields) {
    formRef.resetFields();
  }

  // Reset form data to initial state
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === "string") {
      formData[key] = "";
    } else if (typeof formData[key] === "boolean") {
      formData[key] = false;
    } else if (Array.isArray(formData[key])) {
      formData[key] = [];
    }
  });
};

// Countdown timer helper
export const useCountdown = (initialSeconds: number = 60) => {
  const countdown = ref(0);
  const isActive = ref(false);
  let timer: NodeJS.Timeout | null = null;

  const start = (seconds: number = initialSeconds) => {
    if (timer) {
      clearInterval(timer);
    }

    countdown.value = seconds;
    isActive.value = true;

    timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        stop();
      }
    }, 1000);
  };

  const stop = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    countdown.value = 0;
    isActive.value = false;
  };

  const reset = () => {
    stop();
    countdown.value = initialSeconds;
  };

  return {
    countdown: readonly(countdown),
    isActive: readonly(isActive),
    start,
    stop,
    reset
  };
};
