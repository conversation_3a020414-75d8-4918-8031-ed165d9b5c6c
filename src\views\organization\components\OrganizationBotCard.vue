<script setup lang="ts"></script>

<template>
  <div class="group">
    <el-card
      class="transform transition-all !rounded-[12px] duration-300 hover:scale-105 hover:shadow-2xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden"
    >
      <template #header>
        <div
          class="bg-gradient-to-r from-emerald-500 to-teal-500 -m-5 mb-4 p-6 text-white"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div
                class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm"
              >
                <i class="el-icon-picture text-2xl" />
              </div>
              <div>
                <h3 class="text-xl font-bold">
                  {{ $t("Organization AI Assistant") }}
                </h3>
                <p class="text-emerald-100 text-sm">GPT-4</p>
              </div>
            </div>
            <el-tag
              type="success"
              effect="light"
              size="small"
              class="bg-green-100 text-green-800 border-green-200"
            >
              {{ $t("Active") }}
            </el-tag>
          </div>
        </div>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <i class="el-icon-user text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">{{ $t("Creator") }}</p>
              <p class="font-semibold text-gray-800">
                {{ $t("Organization Admin") }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <i class="el-icon-calendar text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">{{ $t("Created Date") }}</p>
              <p class="font-semibold text-gray-800">22/03/2024</p>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <i class="el-icon-chat-line-round text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">{{ $t("Conversations") }}</p>
              <p class="font-semibold text-gray-800">1,234</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <i class="el-icon-star text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">{{ $t("Rating") }}</p>
              <p class="font-semibold text-gray-800">4.8/5</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-3">
          <p class="text-xs text-gray-500 mb-1">{{ $t("Description") }}</p>
          <p class="text-sm text-gray-700 leading-relaxed">
            {{
              $t(
                "Advanced AI assistant for organization management and automation tasks."
              )
            }}
          </p>
        </div>

        <div class="flex space-x-2">
          <el-button
            type="primary"
            size="small"
            class="flex-1 !bg-gradient-to-r !from-emerald-500 !to-teal-500 !border-0 hover:!from-emerald-600 hover:!to-teal-600"
          >
            <i class="el-icon-chat-line-round mr-1" />
            {{ $t("Chat Now") }}
          </el-button>
          <el-button
            type="default"
            size="small"
            class="!border-gray-200 hover:!border-emerald-400 hover:!text-emerald-600"
          >
            <i class="el-icon-setting mr-1" />
            {{ $t("Settings") }}
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>
