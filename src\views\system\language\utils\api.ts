import { http } from "@/utils/http";
import type { Result } from "@/utils/response";

// Public API endpoints (no authentication required)
export const getLanguages = () => {
  return http.request<Result>("get", "/api/v1/languages");
};

export const getTranslations = (locale: string) => {
  return http.request<Result>("get", `/api/v1/translations/${locale}.json`);
};

// Dropdown API for public use
export const getLanguagesDropdown = () => {
  return http.request<Result>("get", "/api/v1/languages/dropdown");
};
