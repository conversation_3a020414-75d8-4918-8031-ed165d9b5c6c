<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Language Code")),
    prop: "code",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input language code"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 10,
        message: $t("Length must be between 2 and 10 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 15 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    colProps: { span: 9 }
  },
  {
    label: computed(() => $t("Language Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input language name"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length must be between 2 and 100 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Native Name")),
    prop: "nativeName",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input native name"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length must be between 2 and 100 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Flag")),
    prop: "flag",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Direction")),
    prop: "direction",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Left to Right"), value: "ltr" },
      { label: $t("Right to Left"), value: "rtl" }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Is Default")),
    prop: "isDefault",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Sort Order")),
    prop: "sortOrder",
    valueType: "input-number",
    fieldProps: {
      placeholder: "",
      min: 0,
      max: 999
    },
    colProps: { span: 6 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
