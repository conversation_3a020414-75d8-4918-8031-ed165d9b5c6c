# Vue.js Integration Examples

This guide shows how to integrate the ProcMS Chatbot Widget with Vue.js applications (Vue 2 and Vue 3).

## Installation

```bash
npm install @procms/chatbot-widget
```

## Vue 3 Integration

### Basic Vue 3 Component

```vue
<template>
  <div class="chatbot-wrapper">
    <div 
      ref="chatbotContainer"
      class="chatbot-container"
      :style="{ width: width + 'px', height: height + 'px' }"
    >
      <div v-if="isLoading" class="loading">
        Loading chatbot...
      </div>
      <div v-if="error" class="error">
        Error: {{ error }}
        <button @click="retry">Retry</button>
      </div>
    </div>
    
    <div v-if="showControls" class="controls">
      <button @click="toggleTheme">
        Switch to {{ theme === 'light' ? 'dark' : 'light' }} theme
      </button>
      <button @click="unmountWidget" :disabled="!widget">
        Unmount Widget
      </button>
    </div>

    <div v-if="messages.length > 0" class="message-log">
      <h4>Recent Messages ({{ messages.length }})</h4>
      <ul>
        <li v-for="(message, index) in messages.slice(-5)" :key="index">
          <strong>{{ message.type }}:</strong> {{ message.content }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ProcmsChatbot } from '@procms/chatbot-widget'
import '@procms/chatbot-widget/dist/style.css'

// Props
const props = defineProps({
  botUuid: {
    type: String,
    required: true
  },
  apiKey: {
    type: String,
    required: true
  },
  theme: {
    type: String,
    default: 'light',
    validator: (value) => ['light', 'dark', 'auto'].includes(value)
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 600
  },
  showControls: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['message', 'error', 'ready', 'mounted', 'unmounted'])

// Reactive data
const chatbotContainer = ref(null)
const widget = ref(null)
const isLoading = ref(false)
const error = ref(null)
const messages = ref([])
const currentTheme = ref(props.theme)

// Methods
const loadWidget = async () => {
  try {
    isLoading.value = true
    error.value = null

    if (widget.value) {
      widget.value.unmount()
    }

    const newWidget = await ProcmsChatbot.create({
      botUuid: props.botUuid,
      apiKey: props.apiKey,
      theme: currentTheme.value,
      onMessage: (message) => {
        messages.value.push(message)
        emit('message', message)
      },
      onError: (err) => {
        error.value = err.message
        emit('error', err)
      },
      onReady: () => {
        emit('ready')
      }
    }, chatbotContainer.value)

    widget.value = newWidget
    emit('mounted', newWidget)
  } catch (err) {
    error.value = err.message
    emit('error', err)
  } finally {
    isLoading.value = false
  }
}

const unmountWidget = () => {
  if (widget.value) {
    widget.value.unmount()
    widget.value = null
    emit('unmounted')
  }
}

const toggleTheme = () => {
  currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
  if (widget.value) {
    widget.value.setTheme(currentTheme.value)
  }
}

const retry = () => {
  error.value = null
  loadWidget()
}

// Lifecycle
onMounted(async () => {
  await nextTick()
  if (chatbotContainer.value) {
    loadWidget()
  }
})

onUnmounted(() => {
  unmountWidget()
})

// Watchers
watch(() => props.theme, (newTheme) => {
  currentTheme.value = newTheme
  if (widget.value) {
    widget.value.setTheme(newTheme)
  }
})

watch([() => props.botUuid, () => props.apiKey], () => {
  if (chatbotContainer.value) {
    loadWidget()
  }
})
</script>

<style scoped>
.chatbot-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chatbot-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.loading, .error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.error {
  flex-direction: column;
  gap: 10px;
  color: #d32f2f;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.controls button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.controls button:hover {
  background: #f5f5f5;
}

.controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message-log {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.message-log h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.message-log ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.message-log li {
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.message-log li:last-child {
  border-bottom: none;
}
</style>
```

### Usage in Vue 3 App

```vue
<template>
  <div id="app">
    <header>
      <h1>Vue 3 Chatbot Demo</h1>
      <div class="theme-selector">
        <label>Theme: </label>
        <select v-model="selectedTheme">
          <option value="light">Light</option>
          <option value="dark">Dark</option>
          <option value="auto">Auto</option>
        </select>
      </div>
    </header>

    <main>
      <div class="content">
        <h2>Welcome to our Vue 3 app!</h2>
        <p>Chat with our bot below:</p>
      </div>

      <ChatbotWidget
        :bot-uuid="botConfig.uuid"
        :api-key="botConfig.apiKey"
        :theme="selectedTheme"
        :show-controls="true"
        @message="handleMessage"
        @error="handleError"
        @ready="handleReady"
      />
    </main>

    <div v-if="notifications.length > 0" class="notifications">
      <div 
        v-for="notification in notifications" 
        :key="notification.id"
        :class="['notification', notification.type]"
      >
        {{ notification.message }}
        <button @click="removeNotification(notification.id)">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import ChatbotWidget from './components/ChatbotWidget.vue'

const selectedTheme = ref('light')
const notifications = ref([])

const botConfig = reactive({
  uuid: 'your-bot-uuid-here',
  apiKey: 'pk_live_your_api_key_here'
})

let notificationId = 0

const addNotification = (message, type = 'info') => {
  const id = ++notificationId
  notifications.value.push({ id, message, type })
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    removeNotification(id)
  }, 5000)
}

const removeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const handleMessage = (message) => {
  console.log('New message:', message)
  
  // Track in analytics
  if (window.gtag) {
    window.gtag('event', 'chatbot_message', {
      message_type: message.type,
      conversation_id: message.conversationId
    })
  }
}

const handleError = (error) => {
  console.error('Chatbot error:', error)
  addNotification(`Chatbot error: ${error.message}`, 'error')
}

const handleReady = () => {
  console.log('Chatbot is ready')
  addNotification('Chatbot loaded successfully!', 'success')
}
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.theme-selector select {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.content {
  margin-bottom: 40px;
}

.notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  padding: 12px 16px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 300px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.notification.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.notification.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.notification button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  margin-left: 10px;
}
</style>
```

## Vue 2 Integration

### Vue 2 Component (Options API)

```vue
<template>
  <div class="chatbot-wrapper">
    <div 
      ref="chatbotContainer"
      class="chatbot-container"
      :style="containerStyle"
    >
      <div v-if="isLoading" class="loading">
        Loading chatbot...
      </div>
      <div v-if="error" class="error">
        Error: {{ error }}
        <button @click="retry">Retry</button>
      </div>
    </div>
    
    <div v-if="showControls" class="controls">
      <button @click="toggleTheme">
        Switch to {{ theme === 'light' ? 'dark' : 'light' }} theme
      </button>
      <button @click="unmountWidget" :disabled="!widget">
        Unmount Widget
      </button>
    </div>
  </div>
</template>

<script>
import { ProcmsChatbot } from '@procms/chatbot-widget'
import '@procms/chatbot-widget/dist/style.css'

export default {
  name: 'ChatbotWidget',
  props: {
    botUuid: {
      type: String,
      required: true
    },
    apiKey: {
      type: String,
      required: true
    },
    theme: {
      type: String,
      default: 'light',
      validator: (value) => ['light', 'dark', 'auto'].includes(value)
    },
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 600
    },
    showControls: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      widget: null,
      isLoading: false,
      error: null,
      messages: [],
      currentTheme: this.theme
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px'
      }
    }
  },
  watch: {
    theme(newTheme) {
      this.currentTheme = newTheme
      if (this.widget) {
        this.widget.setTheme(newTheme)
      }
    },
    botUuid() {
      this.loadWidget()
    },
    apiKey() {
      this.loadWidget()
    }
  },
  async mounted() {
    await this.$nextTick()
    if (this.$refs.chatbotContainer) {
      this.loadWidget()
    }
  },
  beforeDestroy() {
    this.unmountWidget()
  },
  methods: {
    async loadWidget() {
      try {
        this.isLoading = true
        this.error = null

        if (this.widget) {
          this.widget.unmount()
        }

        const widget = await ProcmsChatbot.create({
          botUuid: this.botUuid,
          apiKey: this.apiKey,
          theme: this.currentTheme,
          onMessage: (message) => {
            this.messages.push(message)
            this.$emit('message', message)
          },
          onError: (err) => {
            this.error = err.message
            this.$emit('error', err)
          },
          onReady: () => {
            this.$emit('ready')
          }
        }, this.$refs.chatbotContainer)

        this.widget = widget
        this.$emit('mounted', widget)
      } catch (err) {
        this.error = err.message
        this.$emit('error', err)
      } finally {
        this.isLoading = false
      }
    },
    unmountWidget() {
      if (this.widget) {
        this.widget.unmount()
        this.widget = null
        this.$emit('unmounted')
      }
    },
    toggleTheme() {
      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light'
      if (this.widget) {
        this.widget.setTheme(this.currentTheme)
      }
    },
    retry() {
      this.error = null
      this.loadWidget()
    }
  }
}
</script>

<style scoped>
/* Same styles as Vue 3 version */
</style>
```

## Composables (Vue 3)

### useChatbot Composable

```javascript
import { ref, onUnmounted } from 'vue'
import { ProcmsChatbot } from '@procms/chatbot-widget'

export function useChatbot(config = {}) {
  const widget = ref(null)
  const isLoaded = ref(false)
  const isLoading = ref(false)
  const error = ref(null)
  const messages = ref([])

  const createWidget = async (container, options = {}) => {
    try {
      isLoading.value = true
      error.value = null

      if (widget.value) {
        widget.value.unmount()
      }

      const newWidget = await ProcmsChatbot.create({
        ...config,
        ...options,
        onMessage: (message) => {
          messages.value.push(message)
          config.onMessage?.(message)
          options.onMessage?.(message)
        },
        onError: (err) => {
          error.value = err.message
          config.onError?.(err)
          options.onError?.(err)
        },
        onReady: () => {
          isLoaded.value = true
          config.onReady?.()
          options.onReady?.()
        }
      }, container)

      widget.value = newWidget
    } catch (err) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  const createFloatingWidget = async (options = {}) => {
    try {
      isLoading.value = true
      error.value = null

      const newWidget = await ProcmsChatbot.createFloatingWidget({
        ...config,
        ...options,
        onMessage: (message) => {
          messages.value.push(message)
          config.onMessage?.(message)
          options.onMessage?.(message)
        },
        onError: (err) => {
          error.value = err.message
          config.onError?.(err)
          options.onError?.(err)
        },
        onReady: () => {
          isLoaded.value = true
          config.onReady?.()
          options.onReady?.()
        }
      })

      widget.value = newWidget
    } catch (err) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  const unmount = () => {
    if (widget.value) {
      widget.value.unmount()
      widget.value = null
      isLoaded.value = false
    }
  }

  const setTheme = (theme) => {
    if (widget.value) {
      widget.value.setTheme(theme)
    }
  }

  const setCustomTheme = (themeConfig) => {
    if (widget.value) {
      widget.value.setCustomTheme(themeConfig)
    }
  }

  // Cleanup on unmount
  onUnmounted(() => {
    unmount()
  })

  return {
    widget: readonly(widget),
    isLoaded: readonly(isLoaded),
    isLoading: readonly(isLoading),
    error: readonly(error),
    messages: readonly(messages),
    createWidget,
    createFloatingWidget,
    unmount,
    setTheme,
    setCustomTheme
  }
}
```

### Using the Composable

```vue
<template>
  <div>
    <div ref="container" class="chatbot-container"></div>
    
    <div class="controls">
      <button @click="loadWidget" :disabled="isLoading">
        {{ isLoading ? 'Loading...' : 'Load Widget' }}
      </button>
      <button @click="loadFloating" :disabled="isLoading">
        Load Floating
      </button>
      <button @click="unmount" :disabled="!widget">
        Unmount
      </button>
    </div>

    <div v-if="error" class="error">{{ error }}</div>
    <div v-if="isLoaded" class="success">Widget loaded!</div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useChatbot } from '@/composables/useChatbot'

const container = ref(null)

const {
  widget,
  isLoaded,
  isLoading,
  error,
  messages,
  createWidget,
  createFloatingWidget,
  unmount,
  setTheme
} = useChatbot({
  botUuid: 'your-bot-uuid-here',
  apiKey: 'pk_live_your_api_key_here',
  onMessage: (message) => {
    console.log('Message received:', message)
  }
})

const loadWidget = () => {
  if (container.value) {
    createWidget(container.value)
  }
}

const loadFloating = () => {
  createFloatingWidget({ position: 'bottom-right' })
}
</script>
```

## Vuex Integration

### Store Module

```javascript
// store/modules/chatbot.js
import { ProcmsChatbot } from '@procms/chatbot-widget'

const state = {
  widget: null,
  isLoaded: false,
  isLoading: false,
  error: null,
  messages: [],
  theme: 'light',
  config: {
    botUuid: '',
    apiKey: ''
  }
}

const mutations = {
  SET_WIDGET(state, widget) {
    state.widget = widget
  },
  SET_LOADING(state, loading) {
    state.isLoading = loading
  },
  SET_LOADED(state, loaded) {
    state.isLoaded = loaded
  },
  SET_ERROR(state, error) {
    state.error = error
  },
  ADD_MESSAGE(state, message) {
    state.messages.push(message)
  },
  SET_THEME(state, theme) {
    state.theme = theme
  },
  SET_CONFIG(state, config) {
    state.config = { ...state.config, ...config }
  },
  CLEAR_MESSAGES(state) {
    state.messages = []
  }
}

const actions = {
  async createFloatingWidget({ commit, state }, options = {}) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)

      if (state.widget) {
        state.widget.unmount()
      }

      const widget = await ProcmsChatbot.createFloatingWidget({
        ...state.config,
        theme: state.theme,
        ...options,
        onMessage: (message) => {
          commit('ADD_MESSAGE', message)
        },
        onError: (error) => {
          commit('SET_ERROR', error.message)
        },
        onReady: () => {
          commit('SET_LOADED', true)
        }
      })

      commit('SET_WIDGET', widget)
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async createEmbeddedWidget({ commit, state }, { container, options = {} }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)

      if (state.widget) {
        state.widget.unmount()
      }

      const widget = await ProcmsChatbot.create({
        ...state.config,
        theme: state.theme,
        ...options,
        onMessage: (message) => {
          commit('ADD_MESSAGE', message)
        },
        onError: (error) => {
          commit('SET_ERROR', error.message)
        },
        onReady: () => {
          commit('SET_LOADED', true)
        }
      }, container)

      commit('SET_WIDGET', widget)
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  unmountWidget({ commit, state }) {
    if (state.widget) {
      state.widget.unmount()
      commit('SET_WIDGET', null)
      commit('SET_LOADED', false)
    }
  },

  setTheme({ commit, state }, theme) {
    commit('SET_THEME', theme)
    if (state.widget) {
      state.widget.setTheme(theme)
    }
  },

  updateConfig({ commit }, config) {
    commit('SET_CONFIG', config)
  }
}

const getters = {
  isWidgetActive: state => !!state.widget,
  messageCount: state => state.messages.length,
  lastMessage: state => state.messages[state.messages.length - 1] || null
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

### Using Vuex Store

```vue
<template>
  <div>
    <button @click="createFloating" :disabled="isLoading">
      {{ isLoading ? 'Loading...' : 'Create Floating Widget' }}
    </button>
    
    <div class="theme-controls">
      <button 
        v-for="themeOption in themes" 
        :key="themeOption"
        @click="setTheme(themeOption)"
        :class="{ active: theme === themeOption }"
      >
        {{ themeOption }}
      </button>
    </div>

    <div v-if="error" class="error">{{ error }}</div>
    <div class="stats">Messages: {{ messageCount }}</div>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  data() {
    return {
      themes: ['light', 'dark', 'auto']
    }
  },
  computed: {
    ...mapState('chatbot', ['isLoading', 'error', 'theme']),
    ...mapGetters('chatbot', ['messageCount'])
  },
  methods: {
    ...mapActions('chatbot', ['createFloatingWidget', 'setTheme']),
    createFloating() {
      this.createFloatingWidget({ position: 'bottom-right' })
    }
  },
  created() {
    // Set initial config
    this.$store.dispatch('chatbot/updateConfig', {
      botUuid: 'your-bot-uuid-here',
      apiKey: 'pk_live_your_api_key_here'
    })
  }
}
</script>
```

## Best Practices

1. **Lifecycle Management**: Always unmount widgets in beforeDestroy/onUnmounted
2. **Error Handling**: Implement proper error boundaries and user feedback
3. **Performance**: Use v-if instead of v-show for conditional rendering
4. **Reactivity**: Watch for prop changes and update widget accordingly
5. **State Management**: Use Vuex/Pinia for complex state management
6. **TypeScript**: Use Vue 3 with TypeScript for better DX
7. **Testing**: Mock the widget library for unit tests
8. **Accessibility**: Ensure proper ARIA labels and keyboard navigation

## Common Issues

### Widget Not Mounting
- Ensure container element exists before mounting
- Use $nextTick() to wait for DOM updates
- Check console for JavaScript errors

### Memory Leaks
- Always unmount widgets in lifecycle hooks
- Remove event listeners properly
- Clear reactive references

### Reactivity Issues
- Use proper watchers for prop changes
- Ensure reactive data is properly declared
- Use Vue.set() for dynamic properties (Vue 2)

## Next Steps

- [Angular Integration](./angular-integration.md)
- [Advanced Examples](./advanced-integration.html)
- [API Reference](../api-reference.md)
