import { h } from "vue";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import dayjs from "dayjs";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    prop: "key",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Translation Key"),
    cellRenderer: ({ row }) => {
      return h("div", { class: "flex flex-col" }, [
        h("div", { class: "font-medium text-gray-900" }, row.key),
        row.group &&
          h("div", { class: "text-xs text-gray-500" }, `Group: ${row.group}`)
      ]);
    }
  },
  {
    prop: "group",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Group"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: "info",
          size: "small"
        },
        () => row.group || "default"
      );
    }
  },
  {
    prop: "text",
    align: "left",
    sortable: false,
    minWidth: 400,
    headerRenderer: () => $t("Translations"),
    cellRenderer: ({ row }) => {
      const translations = row.text || {};
      const locales = Object.keys(translations);

      if (locales.length === 0) {
        return h(
          "div",
          { class: "text-gray-400 italic" },
          $t("No translations")
        );
      }

      return h(
        "div",
        { class: "space-y-1" },
        locales
          .slice(0, 3)
          .map(locale =>
            h("div", { class: "flex items-center gap-2" }, [
              h(
                ElTag,
                {
                  type: "primary",
                  size: "small",
                  class: "!mr-2"
                },
                () => locale.toUpperCase()
              ),
              h(
                "span",
                {
                  class: "text-sm truncate max-w-xs",
                  title: translations[locale]
                },
                translations[locale] || "-"
              )
            ])
          )
          .concat(
            locales.length > 3
              ? [
                  h(
                    "div",
                    { class: "text-xs text-gray-500" },
                    `+${locales.length - 3} more...`
                  )
                ]
              : []
          )
      );
    }
  },
  {
    prop: "createdAt",
    align: "center",
    sortable: true,
    width: 150,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) => {
      return h(
        "div",
        { class: "text-xs text-gray-600" },
        dayjs(row.createdAt).format("DD/MM/YYYY HH:mm")
      );
    }
  },
  {
    prop: "operation",
    fixed: "right",
    width: 100,
    slot: "operation",
    headerRenderer: () => $t("Action")
  }
];
