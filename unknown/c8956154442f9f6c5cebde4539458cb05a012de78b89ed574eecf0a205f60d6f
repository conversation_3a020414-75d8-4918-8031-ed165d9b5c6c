# React Integration Examples

This guide shows how to integrate the ProcMS Chatbot Widget with React applications.

## Installation

```bash
npm install @procms/chatbot-widget
```

## Basic React Component

### Functional Component with Hooks

```jsx
import React, { useEffect, useRef, useState } from 'react';
import { ProcmsChatbot } from '@procms/chatbot-widget';
import '@procms/chatbot-widget/dist/style.css';

const ChatbotWidget = ({ 
  botUuid, 
  apiKey, 
  theme = 'light',
  onMessage,
  onError 
}) => {
  const containerRef = useRef(null);
  const widgetRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadWidget = async () => {
      try {
        if (containerRef.current && !widgetRef.current) {
          const widget = await ProcmsChatbot.create({
            botUuid,
            apiKey,
            theme,
            onMessage: (message) => {
              console.log('New message:', message);
              onMessage?.(message);
            },
            onError: (err) => {
              console.error('Widget error:', err);
              setError(err.message);
              onError?.(err);
            },
            onReady: () => {
              setIsLoaded(true);
            }
          }, containerRef.current);

          widgetRef.current = widget;
        }
      } catch (err) {
        setError(err.message);
        onError?.(err);
      }
    };

    loadWidget();

    // Cleanup
    return () => {
      if (widgetRef.current) {
        widgetRef.current.unmount();
        widgetRef.current = null;
      }
    };
  }, [botUuid, apiKey, theme, onMessage, onError]);

  // Update theme when prop changes
  useEffect(() => {
    if (widgetRef.current && isLoaded) {
      widgetRef.current.setTheme(theme);
    }
  }, [theme, isLoaded]);

  if (error) {
    return (
      <div className="chatbot-error">
        <p>Failed to load chatbot: {error}</p>
        <button onClick={() => window.location.reload()}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="chatbot-container"
      style={{ width: '100%', height: '600px' }}
    >
      {!isLoaded && (
        <div className="chatbot-loading">
          <p>Loading chatbot...</p>
        </div>
      )}
    </div>
  );
};

export default ChatbotWidget;
```

### Usage in App Component

```jsx
import React, { useState } from 'react';
import ChatbotWidget from './components/ChatbotWidget';

function App() {
  const [theme, setTheme] = useState('light');
  const [messages, setMessages] = useState([]);

  const handleMessage = (message) => {
    setMessages(prev => [...prev, message]);
    
    // Track in analytics
    if (window.gtag) {
      window.gtag('event', 'chatbot_message', {
        message_type: message.type,
        conversation_id: message.conversationId
      });
    }
  };

  const handleError = (error) => {
    console.error('Chatbot error:', error);
    // Handle error (show notification, etc.)
  };

  return (
    <div className="App">
      <header>
        <h1>My React App</h1>
        <div>
          <label>Theme: </label>
          <select value={theme} onChange={(e) => setTheme(e.target.value)}>
            <option value="light">Light</option>
            <option value="dark">Dark</option>
            <option value="auto">Auto</option>
          </select>
        </div>
      </header>

      <main>
        <div className="content">
          <h2>Welcome to our website!</h2>
          <p>Chat with our bot for support.</p>
        </div>

        <div className="chatbot-section">
          <ChatbotWidget
            botUuid="your-bot-uuid-here"
            apiKey="pk_live_your_api_key_here"
            theme={theme}
            onMessage={handleMessage}
            onError={handleError}
          />
        </div>
      </main>

      {messages.length > 0 && (
        <div className="message-log">
          <h3>Recent Messages ({messages.length})</h3>
          <ul>
            {messages.slice(-5).map((msg, index) => (
              <li key={index}>
                <strong>{msg.type}:</strong> {msg.content}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

export default App;
```

## Advanced React Patterns

### Custom Hook for Chatbot

```jsx
import { useEffect, useRef, useState, useCallback } from 'react';
import { ProcmsChatbot } from '@procms/chatbot-widget';

export const useChatbot = (config) => {
  const [widget, setWidget] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(null);
  const [messages, setMessages] = useState([]);
  const containerRef = useRef(null);

  const mount = useCallback(async (container) => {
    try {
      if (widget) {
        widget.unmount();
      }

      const newWidget = await ProcmsChatbot.create({
        ...config,
        onMessage: (message) => {
          setMessages(prev => [...prev, message]);
          config.onMessage?.(message);
        },
        onError: (err) => {
          setError(err.message);
          config.onError?.(err);
        },
        onReady: () => {
          setIsLoaded(true);
          config.onReady?.();
        }
      }, container);

      setWidget(newWidget);
      setError(null);
    } catch (err) {
      setError(err.message);
    }
  }, [config, widget]);

  const unmount = useCallback(() => {
    if (widget) {
      widget.unmount();
      setWidget(null);
      setIsLoaded(false);
    }
  }, [widget]);

  const setTheme = useCallback((theme) => {
    if (widget && isLoaded) {
      widget.setTheme(theme);
    }
  }, [widget, isLoaded]);

  const setCustomTheme = useCallback((themeConfig) => {
    if (widget && isLoaded) {
      widget.setCustomTheme(themeConfig);
    }
  }, [widget, isLoaded]);

  useEffect(() => {
    return () => {
      if (widget) {
        widget.unmount();
      }
    };
  }, [widget]);

  return {
    mount,
    unmount,
    setTheme,
    setCustomTheme,
    widget,
    isLoaded,
    error,
    messages,
    containerRef
  };
};
```

### Using the Custom Hook

```jsx
import React, { useEffect } from 'react';
import { useChatbot } from './hooks/useChatbot';

const ChatbotPage = () => {
  const {
    mount,
    unmount,
    setTheme,
    isLoaded,
    error,
    messages,
    containerRef
  } = useChatbot({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here',
    theme: 'light',
    onMessage: (message) => {
      console.log('New message:', message);
    }
  });

  useEffect(() => {
    if (containerRef.current) {
      mount(containerRef.current);
    }

    return () => unmount();
  }, [mount, unmount]);

  return (
    <div>
      <div className="controls">
        <button onClick={() => setTheme('light')}>Light Theme</button>
        <button onClick={() => setTheme('dark')}>Dark Theme</button>
      </div>

      <div 
        ref={containerRef}
        style={{ width: '400px', height: '600px', border: '1px solid #ccc' }}
      />

      {error && <div className="error">Error: {error}</div>}
      {isLoaded && <div className="success">Chatbot loaded successfully!</div>}
    </div>
  );
};
```

## Context Provider Pattern

### Chatbot Context

```jsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ProcmsChatbot } from '@procms/chatbot-widget';

const ChatbotContext = createContext();

const chatbotReducer = (state, action) => {
  switch (action.type) {
    case 'SET_WIDGET':
      return { ...state, widget: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] };
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    default:
      return state;
  }
};

export const ChatbotProvider = ({ children, config }) => {
  const [state, dispatch] = useReducer(chatbotReducer, {
    widget: null,
    isLoading: false,
    error: null,
    messages: [],
    theme: config.theme || 'light'
  });

  const createFloatingWidget = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const widget = await ProcmsChatbot.createFloatingWidget({
        ...config,
        theme: state.theme,
        onMessage: (message) => {
          dispatch({ type: 'ADD_MESSAGE', payload: message });
        },
        onError: (error) => {
          dispatch({ type: 'SET_ERROR', payload: error.message });
        }
      });

      dispatch({ type: 'SET_WIDGET', payload: widget });
      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const setTheme = (theme) => {
    dispatch({ type: 'SET_THEME', payload: theme });
    if (state.widget) {
      state.widget.setTheme(theme);
    }
  };

  const destroyWidget = () => {
    if (state.widget) {
      state.widget.unmount();
      dispatch({ type: 'SET_WIDGET', payload: null });
    }
  };

  useEffect(() => {
    return () => {
      if (state.widget) {
        state.widget.unmount();
      }
    };
  }, [state.widget]);

  const value = {
    ...state,
    createFloatingWidget,
    destroyWidget,
    setTheme
  };

  return (
    <ChatbotContext.Provider value={value}>
      {children}
    </ChatbotContext.Provider>
  );
};

export const useChatbotContext = () => {
  const context = useContext(ChatbotContext);
  if (!context) {
    throw new Error('useChatbotContext must be used within a ChatbotProvider');
  }
  return context;
};
```

### Using Context Provider

```jsx
import React from 'react';
import { ChatbotProvider, useChatbotContext } from './context/ChatbotContext';

const ChatbotControls = () => {
  const { 
    createFloatingWidget, 
    destroyWidget, 
    setTheme, 
    isLoading, 
    error, 
    widget,
    messages 
  } = useChatbotContext();

  return (
    <div className="chatbot-controls">
      <div className="buttons">
        <button 
          onClick={createFloatingWidget} 
          disabled={isLoading || widget}
        >
          {isLoading ? 'Loading...' : 'Show Chatbot'}
        </button>
        
        <button 
          onClick={destroyWidget} 
          disabled={!widget}
        >
          Hide Chatbot
        </button>
      </div>

      <div className="theme-controls">
        <button onClick={() => setTheme('light')}>Light</button>
        <button onClick={() => setTheme('dark')}>Dark</button>
        <button onClick={() => setTheme('auto')}>Auto</button>
      </div>

      {error && <div className="error">Error: {error}</div>}
      
      <div className="stats">
        Messages: {messages.length}
      </div>
    </div>
  );
};

const App = () => {
  return (
    <ChatbotProvider config={{
      botUuid: 'your-bot-uuid-here',
      apiKey: 'pk_live_your_api_key_here'
    }}>
      <div className="app">
        <h1>React Chatbot App</h1>
        <ChatbotControls />
      </div>
    </ChatbotProvider>
  );
};

export default App;
```

## TypeScript Support

### Type Definitions

```typescript
import { ProcmsChatbot, WidgetConfig, ChatMessage } from '@procms/chatbot-widget';

interface ChatbotProps {
  botUuid: string;
  apiKey: string;
  theme?: 'light' | 'dark' | 'auto';
  onMessage?: (message: ChatMessage) => void;
  onError?: (error: Error) => void;
  onReady?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

const TypedChatbotWidget: React.FC<ChatbotProps> = ({
  botUuid,
  apiKey,
  theme = 'light',
  onMessage,
  onError,
  onReady,
  className,
  style
}) => {
  // Component implementation
};
```

## Testing with Jest and React Testing Library

```jsx
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatbotWidget from './ChatbotWidget';

// Mock the widget library
jest.mock('@procms/chatbot-widget', () => ({
  ProcmsChatbot: {
    create: jest.fn(() => Promise.resolve({
      unmount: jest.fn(),
      setTheme: jest.fn(),
      on: jest.fn(),
      off: jest.fn()
    }))
  }
}));

describe('ChatbotWidget', () => {
  const defaultProps = {
    botUuid: 'test-bot-uuid',
    apiKey: 'pk_test_api_key'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    render(<ChatbotWidget {...defaultProps} />);
    expect(screen.getByText('Loading chatbot...')).toBeInTheDocument();
  });

  test('calls onMessage when message is received', async () => {
    const onMessage = jest.fn();
    render(<ChatbotWidget {...defaultProps} onMessage={onMessage} />);

    // Simulate message received
    const mockWidget = await import('@procms/chatbot-widget');
    const createCall = mockWidget.ProcmsChatbot.create.mock.calls[0];
    const config = createCall[0];
    
    const testMessage = { type: 'bot', content: 'Hello!' };
    config.onMessage(testMessage);

    expect(onMessage).toHaveBeenCalledWith(testMessage);
  });

  test('handles theme changes', async () => {
    const { rerender } = render(<ChatbotWidget {...defaultProps} theme="light" />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading chatbot...')).not.toBeInTheDocument();
    });

    rerender(<ChatbotWidget {...defaultProps} theme="dark" />);

    // Verify setTheme was called
    const mockWidget = await import('@procms/chatbot-widget');
    const widgetInstance = await mockWidget.ProcmsChatbot.create();
    expect(widgetInstance.setTheme).toHaveBeenCalledWith('dark');
  });
});
```

## Best Practices

1. **Error Boundaries**: Wrap chatbot components in error boundaries
2. **Lazy Loading**: Use React.lazy() for code splitting
3. **Memoization**: Use React.memo() for performance optimization
4. **Cleanup**: Always unmount widgets in useEffect cleanup
5. **TypeScript**: Use proper type definitions for better DX
6. **Testing**: Mock the widget library for unit tests
7. **State Management**: Use Context API or Redux for complex state
8. **Performance**: Monitor widget loading times and errors

## Common Issues

### Widget Not Loading
- Check console for errors
- Verify API key and bot UUID
- Ensure container element exists

### Memory Leaks
- Always unmount widgets in cleanup functions
- Remove event listeners properly

### CSS Conflicts
- Use CSS modules or styled-components
- Ensure widget CSS is loaded after your styles

### TypeScript Errors
- Install type definitions: `npm install @types/procms-chatbot-widget`
- Use proper type imports

## Next Steps

- [Vue.js Integration](./vue-integration.md)
- [Angular Integration](./angular-integration.md)
- [Advanced Examples](./advanced-integration.html)
