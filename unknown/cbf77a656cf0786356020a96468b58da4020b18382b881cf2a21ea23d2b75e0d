/**
 * Base Styles for ProcMS Widget
 * Isolated styles to prevent conflicts with host website
 */

/* === CSS Reset for Widget Container === */
.procms-chatbot-widget {
  /* Reset all inherited styles */
  all: initial;
  
  /* Set base properties */
  box-sizing: border-box;
  font-family: var(--procms-font-family);
  font-size: var(--procms-font-size-base);
  line-height: var(--procms-line-height-normal);
  color: var(--procms-text-primary);
  background: var(--procms-widget-bg);
  
  /* Ensure proper stacking */
  position: relative;
  z-index: var(--procms-z-modal);
  
  /* Prevent text selection issues */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Prevent zoom on mobile */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  
  /* Apply to all child elements */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  /* Reset common elements */
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: var(--procms-font-weight-semibold);
    line-height: var(--procms-line-height-tight);
    margin: 0;
  }
  
  p {
    margin: 0;
    line-height: var(--procms-line-height-normal);
  }
  
  a {
    color: var(--procms-primary);
    text-decoration: none;
    transition: color var(--procms-transition-fast);
    
    &:hover {
      color: var(--procms-primary-hover);
      text-decoration: underline;
    }
    
    &:focus {
      outline: 2px solid var(--procms-primary);
      outline-offset: 2px;
    }
  }
  
  button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    color: inherit;
    
    &:focus {
      outline: 2px solid var(--procms-primary);
      outline-offset: 2px;
    }
    
    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
  
  input, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
    border: none;
    background: none;
    color: inherit;
    
    &:focus {
      outline: none;
    }
    
    &::placeholder {
      color: var(--procms-text-muted);
      opacity: 1;
    }
    
    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
  
  img {
    max-width: 100%;
    height: auto;
    border: none;
  }
  
  svg {
    display: block;
    vertical-align: middle;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: var(--procms-scrollbar-width);
    height: var(--procms-scrollbar-width);
  }
  
  ::-webkit-scrollbar-track {
    background: var(--procms-scrollbar-track);
    border-radius: var(--procms-radius-base);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--procms-scrollbar-thumb);
    border-radius: var(--procms-radius-base);
    transition: background var(--procms-transition-fast);
    
    &:hover {
      background: var(--procms-scrollbar-thumb-hover);
    }
  }
  
  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: var(--procms-scrollbar-thumb) var(--procms-scrollbar-track);
}

/* === Widget Container Variants === */
.procms-chatbot-widget {
  /* Default embedded widget */
  &.procms-widget-embedded {
    width: 100%;
    height: 100%;
    border-radius: var(--procms-widget-border-radius);
    box-shadow: var(--procms-widget-shadow);
    overflow: hidden;
  }
  
  /* Floating widget */
  &.procms-widget-floating {
    position: fixed;
    width: var(--procms-widget-width);
    height: var(--procms-widget-height);
    max-width: var(--procms-widget-max-width);
    max-height: var(--procms-widget-max-height);
    border-radius: var(--procms-widget-border-radius);
    box-shadow: var(--procms-widget-shadow-lg);
    overflow: hidden;
    z-index: var(--procms-z-modal);
    
    /* Position variants */
    &.procms-position-bottom-right {
      bottom: var(--procms-space-6);
      right: var(--procms-space-6);
    }
    
    &.procms-position-bottom-left {
      bottom: var(--procms-space-6);
      left: var(--procms-space-6);
    }
    
    &.procms-position-top-right {
      top: var(--procms-space-6);
      right: var(--procms-space-6);
    }
    
    &.procms-position-top-left {
      top: var(--procms-space-6);
      left: var(--procms-space-6);
    }
    
    &.procms-position-center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  
  /* Fullscreen widget */
  &.procms-widget-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    z-index: var(--procms-z-modal);
  }
  
  /* Minimized state */
  &.procms-widget-minimized {
    height: var(--procms-header-height);
    overflow: hidden;
    
    .procms-widget-body,
    .procms-widget-footer {
      display: none;
    }
  }
}

/* === Responsive Design === */
@media (max-width: 768px) {
  .procms-chatbot-widget.procms-widget-floating {
    --procms-widget-width: calc(100vw - var(--procms-space-8));
    --procms-widget-height: calc(100vh - var(--procms-space-16));
    
    &.procms-position-bottom-right,
    &.procms-position-bottom-left,
    &.procms-position-top-right,
    &.procms-position-top-left {
      bottom: var(--procms-space-4);
      left: var(--procms-space-4);
      right: var(--procms-space-4);
      top: var(--procms-space-8);
      width: auto;
      height: auto;
    }
  }
}

@media (max-width: 480px) {
  .procms-chatbot-widget.procms-widget-floating {
    &.procms-position-bottom-right,
    &.procms-position-bottom-left,
    &.procms-position-top-right,
    &.procms-position-top-left {
      bottom: 0;
      left: 0;
      right: 0;
      top: 0;
      border-radius: 0;
      width: 100vw;
      height: 100vh;
    }
  }
}

/* === Accessibility === */
@media (prefers-reduced-motion: reduce) {
  .procms-chatbot-widget {
    --procms-transition-fast: 0ms;
    --procms-transition-base: 0ms;
    --procms-transition-slow: 0ms;
    
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@media (prefers-contrast: high) {
  .procms-chatbot-widget {
    --procms-widget-border: #000000;
    --procms-text-primary: #000000;
    --procms-text-secondary: #000000;
    
    [data-procms-theme="dark"] & {
      --procms-widget-border: #ffffff;
      --procms-text-primary: #ffffff;
      --procms-text-secondary: #ffffff;
    }
  }
}

/* === Print Styles === */
@media print {
  .procms-chatbot-widget {
    display: none !important;
  }
}

/* === Focus Management === */
.procms-chatbot-widget {
  &:focus-within {
    outline: 2px solid var(--procms-primary);
    outline-offset: 2px;
  }
  
  /* Skip link for accessibility */
  .procms-skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--procms-primary);
    color: var(--procms-text-inverse);
    padding: var(--procms-space-2) var(--procms-space-4);
    border-radius: var(--procms-radius-base);
    text-decoration: none;
    z-index: var(--procms-z-tooltip);
    transition: top var(--procms-transition-fast);
    
    &:focus {
      top: 6px;
    }
  }
}

/* === Loading States === */
.procms-widget-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--procms-text-secondary);
  
  .procms-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--procms-gray-200);
    border-top: 3px solid var(--procms-primary);
    border-radius: 50%;
    animation: procms-spin 1s linear infinite;
    margin-bottom: var(--procms-space-4);
  }
}

@keyframes procms-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === Error States === */
.procms-widget-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--procms-space-6);
  text-align: center;
  background: var(--procms-error-50);
  border: 1px solid var(--procms-error-500);
  border-radius: var(--procms-widget-border-radius);
  color: var(--procms-error-600);
  
  .procms-error-icon {
    font-size: var(--procms-font-size-2xl);
    margin-bottom: var(--procms-space-4);
  }
  
  .procms-error-title {
    font-weight: var(--procms-font-weight-semibold);
    margin-bottom: var(--procms-space-2);
  }
  
  .procms-error-message {
    color: var(--procms-text-secondary);
    margin-bottom: var(--procms-space-4);
  }
  
  .procms-error-retry {
    background: var(--procms-primary);
    color: var(--procms-text-inverse);
    padding: var(--procms-space-2) var(--procms-space-4);
    border-radius: var(--procms-radius-base);
    transition: background var(--procms-transition-fast);
    
    &:hover {
      background: var(--procms-primary-hover);
    }
  }
}
