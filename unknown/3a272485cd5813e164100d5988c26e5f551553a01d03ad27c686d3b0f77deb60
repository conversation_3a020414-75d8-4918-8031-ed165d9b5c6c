/**
 * ProcMS Chatbot Widget - Main Entry Point
 * Hybrid implementation supporting both JavaScript Widget and Iframe
 */

import { WidgetLoader } from './core/widget-loader';
import { detectWidgetMode, getDetectionReport } from './core/widget-detector';
import type { WidgetConfig, BotConfig, WidgetMode } from './types';

export class ProcmsChatbotWidget extends WidgetLoader {
  constructor(config: WidgetConfig) {
    super(config);
  }
}

/**
 * Static utility class for easy integration
 */
export class ProcmsChatbot {

  /**
   * Create and mount widget in one call
   */
  static async create(config: WidgetConfig, selector: string | HTMLElement): Promise<ProcmsChatbotWidget> {
    const widget = new ProcmsChatbotWidget(config);
    await widget.mount(selector);
    return widget;
  }

  /**
   * Generate iframe embed code
   */
  static generateIframeCode(config: WidgetConfig): string {
    const params = new URLSearchParams({
      bot_uuid: config.botUuid,
      api_key: config.apiKey,
      theme: config.theme || 'light',
      position: config.position || 'bottom-right',
      show_header: config.showHeader ? '1' : '0',
      show_avatar: config.showAvatar ? '1' : '0',
      auto_open: config.autoOpen ? '1' : '0',
      user_id: config.userId || '',
      domain: window.location.hostname
    });

    const iframeUrl = config.iframeUrl || 'https://widget.procms.com';
    const width = config.width || 400;
    const height = config.height || 600;

    return `<iframe
      src="${iframeUrl}?${params.toString()}"
      width="${width}"
      height="${height}"
      frameborder="0"
      style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);"
      title="ProcMS Chatbot">
    </iframe>`;
  }

  /**
   * Detect best widget mode for current environment
   */
  static detectMode(config: WidgetConfig): WidgetMode {
    return detectWidgetMode(config);
  }

  /**
   * Get detailed detection report for debugging
   */
  static getDetectionReport(config: WidgetConfig) {
    return getDetectionReport(config);
  }

  /**
   * Create floating chat button widget
   */
  static async createFloatingWidget(config: WidgetConfig): Promise<ProcmsChatbotWidget> {
    // Create floating container
    const container = document.createElement('div');
    container.id = 'procms-floating-widget';
    container.style.cssText = `
      position: fixed;
      ${config.position === 'bottom-left' ? 'left: 20px;' : 'right: 20px;'}
      ${config.position?.includes('top') ? 'top: 20px;' : 'bottom: 20px;'}
      width: ${config.width || 400}px;
      height: ${config.height || 600}px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    document.body.appendChild(container);

    // Create widget
    const widget = await ProcmsChatbot.create(config, container);

    // Add cleanup on page unload
    window.addEventListener('beforeunload', () => {
      widget.unmount();
      container.remove();
    });

    return widget;
  }

  /**
   * Initialize widget with automatic setup
   */
  static async init(config: WidgetConfig): Promise<ProcmsChatbotWidget> {
    // Auto-detect container or create floating widget
    if (!config.botUuid || !config.apiKey) {
      throw new Error('Bot UUID and API Key are required');
    }

    // If no specific container, create floating widget
    const container = document.querySelector('#procms-chatbot') ||
                     document.querySelector('[data-procms-chatbot]');

    if (container) {
      return ProcmsChatbot.create(config, container);
    } else {
      return ProcmsChatbot.createFloatingWidget(config);
    }
  }
}

// Export types
export type { WidgetConfig, BotConfig, WidgetMode };
export { detectWidgetMode, getDetectionReport };

// Global registration for script tag usage
if (typeof window !== 'undefined') {
  (window as any).ProcmsChatbotWidget = ProcmsChatbotWidget;
  (window as any).ProcmsChatbot = ProcmsChatbot;

  // Auto-initialize if config is provided
  const autoConfig = (window as any).PROCMS_CHATBOT_CONFIG;
  if (autoConfig) {
    document.addEventListener('DOMContentLoaded', () => {
      ProcmsChatbot.init(autoConfig).catch(console.error);
    });
  }
}

// Remove default export to avoid named/default export warning
// Users can import as: import { ProcmsChatbot } from 'procms-chatbot'
