/**
 * Widget Types and Interfaces
 */

export interface WidgetConfig {
  // Required
  botUuid: string;
  apiKey: string;
  
  // Optional
  userId?: string;
  mode?: 'widget' | 'iframe' | 'auto';
  theme?: 'light' | 'dark' | 'auto';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
  
  // UI Options
  showHeader?: boolean;
  showAvatar?: boolean;
  autoOpen?: boolean;
  width?: number | string;
  height?: number | string;
  
  // Advanced
  forceIframe?: boolean;
  iframeUrl?: string;
  customCSS?: string;
  
  // Events
  onMessage?: (data: any) => void;
  onError?: (error: any) => void;
  onOpen?: () => void;
  onClose?: () => void;
  onReady?: () => void;
}

export interface BotConfig {
  uuid: string;
  name: string;
  logo?: string;
  logoUrl?: string;
  description?: string;
  
  // Messages
  greetingMessage: string;
  starterMessages?: string[];
  closingMessage?: string;
  
  // AI Configuration
  aiModel?: {
    name: string;
    provider: string;
  };
  systemPrompt?: string;
  
  // Behavior
  toolCallingMode?: 'auto' | 'none' | 'required';
  parameters?: any;
  
  // Status
  status: 'draft' | 'review' | 'active' | 'paused' | 'banned';
  visibility?: string;
  
  // Theme
  theme?: {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: string;
    fontFamily?: string;
  };
  
  // Metadata
  metadata?: any;
  knowledge?: any;
  createdAt?: string;
  updatedAt?: string;
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  type: 'user' | 'bot' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    tokens?: number;
    model?: string;
    [key: string]: any;
  };
}

export interface ConversationState {
  id: string;
  botUuid: string;
  userId?: string;
  messages: ChatMessage[];
  status: 'active' | 'ended';
  startedAt: Date;
  lastMessageAt?: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  meta?: {
    timestamp?: string;
    requestId?: string;
    rateLimit?: {
      remaining: number;
      resetAt: string;
    };
    pagination?: {
      total: number;
      page: number;
      limit: number;
      hasMore: boolean;
    };
  };
}

export interface WidgetEvents {
  'ready': () => void;
  'mounted': () => void;
  'unmounted': () => void;
  'open': () => void;
  'close': () => void;
  'message': (message: ChatMessage) => void;
  'error': (error: Error) => void;
  'bot-loaded': (config: BotConfig) => void;
  'conversation-started': (conversationId: string) => void;
  'conversation-ended': (conversationId: string) => void;
}

export type WidgetMode = 'widget' | 'iframe';
export type WidgetTheme = 'light' | 'dark' | 'auto';
export type WidgetPosition = 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';

// Additional API-related types
export interface FileUpload {
  fileId: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  downloadUrl: string;
  uploadedAt: string;
}

export interface ConversationSummary {
  messageCount: number;
  duration: number;
  startedAt: string;
  lastMessageAt: string;
  status: 'active' | 'ended' | 'abandoned';
  satisfaction?: number;
  tags?: string[];
  resolved?: boolean;
}

export interface BotAnalytics {
  totalConversations: number;
  totalMessages: number;
  averageResponseTime: number;
  satisfactionScore: number;
  topQuestions: Array<{ question: string; count: number }>;
  timeframe: '1h' | '24h' | '7d' | '30d';
}

export interface MessageOptions {
  messageType?: 'text' | 'quick_reply' | 'postback';
  attachments?: Array<{ type: string; url: string; name?: string }>;
  metadata?: Record<string, any>;
}

export interface ConversationFeedback {
  rating: number;
  comment?: string;
  categories?: string[];
  helpful?: boolean;
  tags?: string[];
  resolved?: boolean;
}
