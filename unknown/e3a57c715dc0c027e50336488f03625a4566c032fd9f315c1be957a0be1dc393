<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useTranslationHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const TranslationDrawerForm = defineAsyncComponent(
  () => import("./components/TranslationDrawerForm.vue")
);

const TranslationFilterForm = defineAsyncComponent(
  () => import("./components/TranslationFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetTranslations,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  translationFormRef,
  handleSubmit,
  handleFilter
} = useTranslationHook();

const handleEdit = (row: any) => {
  drawerValues.value = { ...clone(row, true) };
  drawerVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetTranslations();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Translation Management')"
        :columns="columns"
        @refresh="fnGetTranslations"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('translation.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('translation.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip :content="$t('Bulk Delete')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="
                multipleSelection.length === 0 ||
                (multipleSelection.length > 0 && !hasAuth('translation.delete'))
              "
              @click="() => handleBulkDelete()"
            >
              <IconifyIconOnline
                icon="tabler:trash"
                width="18px"
                :class="{
                  'text-red-700':
                    multipleSelection.length > 0 &&
                    hasAuth('translation.delete')
                }"
              />
            </el-button>
          </el-tooltip>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!hasAuth('translation.edit')"
                      @click="handleEdit(row)"
                    >
                      <IconifyIconOnline
                        icon="material-symbols:edit"
                        class="text-blue-600"
                      />
                      <span class="ml-2">
                        {{ $t("Edit") }}
                      </span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!hasAuth('translation.delete')"
                      @click="handleDelete(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:trash"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Delete") }}
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <TranslationDrawerForm
      ref="translationFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          translationFormRef?.resetForm();
          drawerValues = {
            translations: {}
          };
        }
      "
    />

    <TranslationFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = { isTrashed: 'no' };
          fnGetTranslations();
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
