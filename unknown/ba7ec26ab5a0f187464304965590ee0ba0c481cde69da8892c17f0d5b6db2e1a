# User Info Storage Fix Documentation

## 🐛 **Vấn đề**

<PERSON><PERSON> <PERSON>hi login, thông tin user đượ<PERSON> lưu nhưng khi lấy ra không đủ. Chỉ có một số trường cơ bản như `username`, `avatar`, `nickname`, `roles`, `permissions` đ<PERSON><PERSON><PERSON> lư<PERSON>, trong khi API trả về rất nhiều thông tin khác.

## 🔍 **Nguyên nhân**

### **1. API Response đầy đủ:**
```typescript
interface AuthLoginData {
  user: {
    username: string;
    firstName: string;
    lastName: string;
    fullName: string;
    avatar: string | null;
    birthday: string | null;
    gender: string;
    email: string;
    phone: string | null;
    address: string | null;
    status: string;
    lastLoginAt: string | null;
    lastLoginIp: string | null;
    preferences: any;
    isVerified: boolean;
    newsletterSubscribed: boolean;
    createdAt: string;
    updatedAt: string;
    permissions: Array<string>;
    roles: Array<string>;
    country: any;
  };
  // ...
}
```

### **2. V<PERSON><PERSON> đề trong `setUserKey` function:**
```typescript
// ❌ TRƯỚC ĐÂY - Chỉ lưu 5 trường
function setUserKey({ avatar, username, nickname, roles, permissions }) {
  // Chỉ lưu 5 trường cơ bản
}

// ❌ Khi gọi setUserKey
setUserKey({
  avatar: user.avatar || "",
  username: user.username,
  nickname: user.fullName || user.firstName + " " + user.lastName,
  roles: user.roles || [],
  permissions: user.permissions || []
}); // Mất hết thông tin khác!
```

## ✅ **Giải pháp**

### **1. Cập nhật `setUserKey` function:**
```typescript
// ✅ SAU KHI SỬA - Lưu toàn bộ user data
function setUserKey(userData: any) {
  const userStore = useUserStoreHook();
  userStore.user = {
    ...userData,  // Spread toàn bộ user data
    isRemembered,
    loginDay
  };

  storageLocal().setItem(userKey, {
    refreshToken,
    expires,
    ...userData,  // Spread toàn bộ user data
    isRemembered,
    loginDay
  });
}
```

### **2. Cập nhật cách gọi `setUserKey`:**
```typescript
// ✅ Truyền toàn bộ user object
if ("token" in data && "user" in data) {
  const loginData = data as LoginResponseData;
  const user = loginData.user;
  setUserKey({
    ...user,  // Spread toàn bộ user data
    nickname: user.fullName || user.firstName + " " + user.lastName,
    avatar: user.avatar || ""
  });
}
```

### **3. Thêm getters trong User Store:**
```typescript
getters: {
  // Existing getters
  username: state => state.user?.username ?? "",
  avatar: state => state.user?.avatar ?? "",
  
  // ✅ New getters for additional fields
  firstName: state => state.user?.firstName ?? "",
  lastName: state => state.user?.lastName ?? "",
  fullName: state => state.user?.fullName ?? "",
  email: state => state.user?.email ?? "",
  phone: state => state.user?.phone ?? "",
  address: state => state.user?.address ?? "",
  birthday: state => state.user?.birthday ?? "",
  gender: state => state.user?.gender ?? "",
  status: state => state.user?.status ?? "",
  isVerified: state => state.user?.isVerified ?? false,
  newsletterSubscribed: state => state.user?.newsletterSubscribed ?? false,
  lastLoginAt: state => state.user?.lastLoginAt ?? "",
  lastLoginIp: state => state.user?.lastLoginIp ?? "",
  preferences: state => state.user?.preferences ?? {},
  country: state => state.user?.country ?? null,
  createdAt: state => state.user?.createdAt ?? "",
  updatedAt: state => state.user?.updatedAt ?? "",
}
```

### **4. Cập nhật `getUserInfo` method:**
```typescript
async getUserInfo() {
  try {
    const result = await getCurrentUser();
    if (result.success && result.data) {
      const userData = useConvertKeyToCamel(result.data.user);
      // ✅ Merge với existing data thay vì replace
      this.user = { ...this.user, ...userData };
      // ✅ Update localStorage với complete data
      const existingData = storageLocal().getItem(userKey) || {};
      storageLocal().setItem(userKey, { ...existingData, ...userData });
      return result;
    }
  } catch (error) {
    console.error("Failed to get user info:", error);
    throw error;
  }
}
```

## 🧪 **Testing**

### **Test Component:** `/test-user-info`
- Hiển thị toàn bộ user data từ store
- Hiển thị raw user object
- Hiển thị localStorage data
- Buttons để refresh/clear/log user info

### **Cách test:**
1. Login vào hệ thống
2. Truy cập `/test-user-info`
3. Kiểm tra xem tất cả thông tin user có hiển thị đầy đủ không
4. Click "Refresh User Info" để test API call
5. Check console logs

## 📊 **Kết quả**

### **Trước khi sửa:**
```javascript
// localStorage chỉ có:
{
  username: "john_doe",
  avatar: "",
  nickname: "John Doe", 
  roles: ["user"],
  permissions: ["read:profile"],
  refreshToken: "...",
  expires: "..."
}
```

### **Sau khi sửa:**
```javascript
// localStorage có đầy đủ:
{
  username: "john_doe",
  firstName: "John",
  lastName: "Doe", 
  fullName: "John Doe",
  email: "<EMAIL>",
  phone: "+1234567890",
  address: "123 Main St",
  birthday: "1990-01-01",
  gender: "male",
  status: "active",
  isVerified: true,
  newsletterSubscribed: false,
  lastLoginAt: "2024-01-01T10:00:00Z",
  lastLoginIp: "***********",
  preferences: {...},
  country: {...},
  createdAt: "2023-01-01T00:00:00Z",
  updatedAt: "2024-01-01T10:00:00Z",
  roles: ["user"],
  permissions: ["read:profile"],
  // ... và tất cả fields khác
}
```

## 🎯 **Lợi ích**

1. **Đầy đủ thông tin:** Tất cả user data từ API được lưu và có thể truy cập
2. **Consistency:** Data trong store và localStorage đồng bộ
3. **Performance:** Không cần gọi API lại để lấy thông tin đã có
4. **Flexibility:** Có thể truy cập bất kỳ field nào của user
5. **Type Safety:** Có getters với proper typing

## 🔧 **Files đã thay đổi**

- `src/utils/auth.ts` - Cập nhật `setUserKey` function
- `src/store/modules/user.ts` - Thêm getters và cập nhật `getUserInfo`
- `src/views/test-user-info.vue` - Test component (có thể xóa sau khi test xong)
- `src/router/modules/home.ts` - Thêm test route (có thể xóa sau khi test xong)
