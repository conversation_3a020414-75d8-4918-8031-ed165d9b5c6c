/**
 * Component Styles for ProcMS Widget
 * Modular component styling with BEM methodology
 */

/* === Widget Header === */
.procms-widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--procms-header-height);
  padding: 0 var(--procms-space-4);
  background: var(--procms-header-bg);
  color: var(--procms-header-text);
  border-bottom: 1px solid var(--procms-widget-border);
  
  &__content {
    display: flex;
    align-items: center;
    gap: var(--procms-space-3);
    flex: 1;
    min-width: 0; /* Allow text truncation */
  }
  
  &__avatar {
    width: var(--procms-avatar-size);
    height: var(--procms-avatar-size);
    border-radius: var(--procms-avatar-radius);
    object-fit: cover;
    flex-shrink: 0;
  }
  
  &__info {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  
  &__title {
    font-size: var(--procms-font-size-base);
    font-weight: var(--procms-font-weight-semibold);
    line-height: var(--procms-line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  &__status {
    font-size: var(--procms-font-size-xs);
    opacity: 0.8;
    line-height: var(--procms-line-height-tight);
  }
  
  &__actions {
    display: flex;
    align-items: center;
    gap: var(--procms-space-2);
  }
  
  &__action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--procms-radius-base);
    color: var(--procms-header-text);
    transition: background var(--procms-transition-fast);
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
    
    &:active {
      background: rgba(255, 255, 255, 0.2);
    }
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

/* === Widget Body === */
.procms-widget-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--procms-widget-bg);
}

/* === Messages Container === */
.procms-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--procms-space-4);
  display: flex;
  flex-direction: column;
  gap: var(--procms-message-spacing);
  
  &:empty::before {
    content: "Start a conversation...";
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--procms-text-muted);
    font-style: italic;
  }
}

/* === Message === */
.procms-message {
  display: flex;
  gap: var(--procms-space-2);
  max-width: 100%;
  
  &--user {
    flex-direction: row-reverse;
    
    .procms-message__content {
      background: var(--procms-user-message-bg);
      color: var(--procms-user-message-text);
      margin-left: auto;
      border-bottom-right-radius: var(--procms-radius-base);
    }
    
    .procms-message__avatar {
      order: -1;
    }
  }
  
  &--bot {
    .procms-message__content {
      background: var(--procms-bot-message-bg);
      color: var(--procms-bot-message-text);
      border: 1px solid var(--procms-bot-message-border);
      border-bottom-left-radius: var(--procms-radius-base);
    }
  }
  
  &--system {
    justify-content: center;
    
    .procms-message__content {
      background: var(--procms-gray-100);
      color: var(--procms-text-muted);
      font-size: var(--procms-font-size-sm);
      padding: var(--procms-space-2) var(--procms-space-3);
      border-radius: var(--procms-radius-base);
      max-width: none;
    }
  }
  
  &__avatar {
    width: var(--procms-avatar-size);
    height: var(--procms-avatar-size);
    border-radius: var(--procms-avatar-radius);
    object-fit: cover;
    flex-shrink: 0;
    align-self: flex-end;
  }
  
  &__content {
    max-width: var(--procms-message-max-width);
    padding: var(--procms-space-3) var(--procms-space-4);
    border-radius: var(--procms-message-radius);
    word-wrap: break-word;
    position: relative;
  }
  
  &__text {
    margin-bottom: var(--procms-space-1);
    line-height: var(--procms-line-height-normal);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    /* Markdown-like formatting */
    strong, b {
      font-weight: var(--procms-font-weight-semibold);
    }
    
    em, i {
      font-style: italic;
    }
    
    code {
      background: rgba(0, 0, 0, 0.1);
      padding: 0.125rem 0.25rem;
      border-radius: var(--procms-radius-sm);
      font-family: var(--procms-font-family-mono);
      font-size: 0.875em;
    }
    
    pre {
      background: rgba(0, 0, 0, 0.05);
      padding: var(--procms-space-3);
      border-radius: var(--procms-radius-base);
      overflow-x: auto;
      margin: var(--procms-space-2) 0;
      
      code {
        background: none;
        padding: 0;
      }
    }
  }
  
  &__meta {
    display: flex;
    align-items: center;
    gap: var(--procms-space-2);
    font-size: var(--procms-font-size-xs);
    color: var(--procms-text-muted);
    margin-top: var(--procms-space-1);
  }
  
  &__time {
    white-space: nowrap;
  }
  
  &__status {
    display: flex;
    align-items: center;
    gap: var(--procms-space-1);
    
    &--sending {
      color: var(--procms-warning-500);
    }
    
    &--sent {
      color: var(--procms-success-500);
    }
    
    &--error {
      color: var(--procms-error-500);
    }
  }
}

/* === Typing Indicator === */
.procms-typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--procms-space-2);
  padding: var(--procms-space-3) var(--procms-space-4);
  
  &__avatar {
    width: var(--procms-avatar-size);
    height: var(--procms-avatar-size);
    border-radius: var(--procms-avatar-radius);
    object-fit: cover;
    flex-shrink: 0;
  }
  
  &__content {
    background: var(--procms-bot-message-bg);
    border: 1px solid var(--procms-bot-message-border);
    border-radius: var(--procms-message-radius);
    border-bottom-left-radius: var(--procms-radius-base);
    padding: var(--procms-space-3) var(--procms-space-4);
  }
  
  &__dots {
    display: flex;
    gap: var(--procms-space-1);
    
    span {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--procms-text-muted);
      animation: procms-typing-bounce 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

@keyframes procms-typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* === Starter Messages === */
.procms-starter-messages {
  display: flex;
  flex-direction: column;
  gap: var(--procms-space-2);
  margin-top: var(--procms-space-4);
  
  &__title {
    font-size: var(--procms-font-size-sm);
    color: var(--procms-text-secondary);
    margin-bottom: var(--procms-space-2);
  }
  
  &__button {
    background: var(--procms-widget-surface);
    border: 1px solid var(--procms-widget-border);
    border-radius: var(--procms-radius-lg);
    padding: var(--procms-space-3) var(--procms-space-4);
    text-align: left;
    color: var(--procms-text-primary);
    transition: all var(--procms-transition-fast);
    
    &:hover {
      background: var(--procms-primary-light);
      border-color: var(--procms-primary);
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

/* === Widget Footer === */
.procms-widget-footer {
  padding: var(--procms-space-4);
  background: var(--procms-widget-surface);
  border-top: 1px solid var(--procms-widget-border);
}

/* === Input Area === */
.procms-input-area {
  display: flex;
  gap: var(--procms-space-2);
  align-items: flex-end;
  
  &__container {
    flex: 1;
    position: relative;
  }
  
  &__input {
    width: 100%;
    min-height: var(--procms-input-height);
    max-height: 120px;
    padding: var(--procms-space-3) var(--procms-space-4);
    background: var(--procms-input-bg);
    border: 1px solid var(--procms-input-border);
    border-radius: var(--procms-input-radius);
    color: var(--procms-text-primary);
    resize: none;
    transition: border-color var(--procms-transition-fast);
    
    &:focus {
      border-color: var(--procms-input-focus-border);
    }
    
    &:disabled {
      background: var(--procms-gray-100);
      cursor: not-allowed;
    }
  }
  
  &__send {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--procms-button-height);
    height: var(--procms-button-height);
    background: var(--procms-button-primary-bg);
    color: var(--procms-button-primary-text);
    border-radius: var(--procms-button-radius);
    transition: all var(--procms-transition-fast);
    flex-shrink: 0;
    
    &:hover:not(:disabled) {
      background: var(--procms-button-primary-hover);
      transform: scale(1.05);
    }
    
    &:active:not(:disabled) {
      transform: scale(0.95);
    }
    
    &:disabled {
      background: var(--procms-gray-300);
      cursor: not-allowed;
      transform: none;
    }
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
  
  &__attachment {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--procms-button-height);
    height: var(--procms-button-height);
    color: var(--procms-text-secondary);
    border-radius: var(--procms-button-radius);
    transition: all var(--procms-transition-fast);
    
    &:hover {
      background: var(--procms-gray-100);
      color: var(--procms-text-primary);
    }
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

/* === File Upload === */
.procms-file-upload {
  &__dropzone {
    border: 2px dashed var(--procms-widget-border);
    border-radius: var(--procms-radius-lg);
    padding: var(--procms-space-6);
    text-align: center;
    transition: all var(--procms-transition-fast);
    
    &--dragover {
      border-color: var(--procms-primary);
      background: var(--procms-primary-light);
    }
  }
  
  &__preview {
    display: flex;
    align-items: center;
    gap: var(--procms-space-3);
    padding: var(--procms-space-3);
    background: var(--procms-gray-50);
    border-radius: var(--procms-radius-base);
    margin-top: var(--procms-space-2);
    
    &__icon {
      width: 32px;
      height: 32px;
      color: var(--procms-primary);
    }
    
    &__info {
      flex: 1;
      min-width: 0;
    }
    
    &__name {
      font-weight: var(--procms-font-weight-medium);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    &__size {
      font-size: var(--procms-font-size-sm);
      color: var(--procms-text-secondary);
    }
    
    &__remove {
      color: var(--procms-error-500);
      
      &:hover {
        background: var(--procms-error-50);
      }
    }
  }
  
  &__progress {
    width: 100%;
    height: 4px;
    background: var(--procms-gray-200);
    border-radius: var(--procms-radius-full);
    overflow: hidden;
    margin-top: var(--procms-space-2);
    
    &__bar {
      height: 100%;
      background: var(--procms-primary);
      transition: width var(--procms-transition-base);
    }
  }
}

/* === Feedback === */
.procms-feedback {
  padding: var(--procms-space-4);
  background: var(--procms-widget-surface);
  border-top: 1px solid var(--procms-widget-border);
  
  &__title {
    font-weight: var(--procms-font-weight-semibold);
    margin-bottom: var(--procms-space-3);
  }
  
  &__rating {
    display: flex;
    gap: var(--procms-space-1);
    margin-bottom: var(--procms-space-3);
    
    &__star {
      width: 24px;
      height: 24px;
      color: var(--procms-gray-300);
      cursor: pointer;
      transition: color var(--procms-transition-fast);
      
      &:hover,
      &--active {
        color: var(--procms-warning-500);
      }
    }
  }
  
  &__comment {
    width: 100%;
    min-height: 80px;
    padding: var(--procms-space-3);
    border: 1px solid var(--procms-widget-border);
    border-radius: var(--procms-radius-base);
    margin-bottom: var(--procms-space-3);
    resize: vertical;
  }
  
  &__actions {
    display: flex;
    gap: var(--procms-space-2);
    justify-content: flex-end;
  }
  
  &__button {
    padding: var(--procms-space-2) var(--procms-space-4);
    border-radius: var(--procms-radius-base);
    font-weight: var(--procms-font-weight-medium);
    transition: all var(--procms-transition-fast);
    
    &--primary {
      background: var(--procms-primary);
      color: var(--procms-text-inverse);
      
      &:hover {
        background: var(--procms-primary-hover);
      }
    }
    
    &--secondary {
      background: var(--procms-gray-100);
      color: var(--procms-text-primary);
      
      &:hover {
        background: var(--procms-gray-200);
      }
    }
  }
}
