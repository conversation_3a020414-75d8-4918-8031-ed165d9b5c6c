<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Widget Integration - ProcMS Chatbot</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .example {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        .example h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-size: 13px;
        }
        .code-block code {
            font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .analytics-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .theme-selector {
            margin: 10px 0;
        }
        .theme-selector select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .user-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .user-info input {
            margin: 5px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ProcMS Chatbot Widget - Advanced Integration Examples</h1>
        <p>This page demonstrates advanced integration patterns and use cases.</p>

        <!-- Example 1: Multiple Bots -->
        <div class="example">
            <h3>1. Multiple Bots on Same Page</h3>
            <p>Deploy different bots for different purposes (support, sales, etc.)</p>
            
            <div class="code-block">
                <code>
// Support Bot
const supportWidget = await ProcmsChatbot.create({
    botUuid: 'support-bot-uuid',
    apiKey: 'pk_live_support_key',
    theme: 'light'
}, '#support-chat');

// Sales Bot
const salesWidget = await ProcmsChatbot.create({
    botUuid: 'sales-bot-uuid',
    apiKey: 'pk_live_sales_key',
    theme: 'dark'
}, '#sales-chat');
                </code>
            </div>

            <div style="display: flex; gap: 20px;">
                <div>
                    <h4>Support Bot</h4>
                    <div id="support-chat" style="width: 350px; height: 500px; border: 1px solid #ddd; border-radius: 8px;"></div>
                    <button class="btn" onclick="loadSupportBot()">Load Support Bot</button>
                </div>
                <div>
                    <h4>Sales Bot</h4>
                    <div id="sales-chat" style="width: 350px; height: 500px; border: 1px solid #ddd; border-radius: 8px;"></div>
                    <button class="btn" onclick="loadSalesBot()">Load Sales Bot</button>
                </div>
            </div>
            <div id="status-1" class="status" style="display: none;"></div>
        </div>

        <!-- Example 2: Analytics Integration -->
        <div class="example">
            <h3>2. Analytics Integration</h3>
            <p>Track chatbot interactions with Google Analytics and custom analytics.</p>
            
            <div class="code-block">
                <code>
const widget = new ProcmsChatbotWidget({
    botUuid: 'analytics-bot-uuid',
    apiKey: 'pk_live_analytics_key',
    onMessage: (message) => {
        // Google Analytics 4
        gtag('event', 'chatbot_message', {
            message_type: message.type,
            conversation_id: message.conversationId,
            user_id: message.userId
        });
        
        // Custom analytics
        analytics.track('Chatbot Interaction', {
            type: message.type,
            content: message.content,
            timestamp: message.timestamp,
            bot_id: 'analytics-bot'
        });
    },
    onConversationStarted: (conversationId) => {
        gtag('event', 'chatbot_conversation_start', {
            conversation_id: conversationId
        });
    }
});
                </code>
            </div>

            <div id="analytics-widget" class="widget-container"></div>
            <button class="btn" onclick="loadAnalyticsWidget()">Load Analytics Widget</button>
            <button class="btn secondary" onclick="unloadAnalyticsWidget()">Unload</button>
            
            <h4>Analytics Events:</h4>
            <div id="analytics-log" class="analytics-panel">
                Analytics events will appear here...
            </div>
            <div id="status-2" class="status" style="display: none;"></div>
        </div>

        <!-- Example 3: Dynamic Theme Switching -->
        <div class="example">
            <h3>3. Dynamic Theme Switching</h3>
            <p>Allow users to switch themes dynamically and persist preferences.</p>
            
            <div class="code-block">
                <code>
// Theme switching with persistence
function switchTheme(theme) {
    widget.setTheme(theme);
    localStorage.setItem('chatbot-theme', theme);
}

// Load saved theme
const savedTheme = localStorage.getItem('chatbot-theme') || 'auto';
widget.setTheme(savedTheme);

// Custom theme with brand colors
widget.setCustomTheme({
    primaryColor: getComputedStyle(document.documentElement)
        .getPropertyValue('--brand-primary'),
    backgroundColor: getComputedStyle(document.documentElement)
        .getPropertyValue('--brand-background')
});
                </code>
            </div>

            <div class="theme-selector">
                <label>Select Theme: </label>
                <select id="theme-select" onchange="switchWidgetTheme()">
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto (System)</option>
                    <option value="custom">Custom Brand</option>
                </select>
            </div>

            <div id="theme-widget" class="widget-container"></div>
            <button class="btn" onclick="loadThemeWidget()">Load Theme Widget</button>
            <button class="btn secondary" onclick="unloadThemeWidget()">Unload</button>
            <div id="status-3" class="status" style="display: none;"></div>
        </div>

        <!-- Example 4: User Context & Personalization -->
        <div class="example">
            <h3>4. User Context & Personalization</h3>
            <p>Pass user information and context to personalize the chat experience.</p>
            
            <div class="code-block">
                <code>
// Get user context from your application
const userContext = {
    userId: getCurrentUserId(),
    name: getCurrentUserName(),
    email: getCurrentUserEmail(),
    plan: getCurrentUserPlan(),
    lastLogin: getCurrentUserLastLogin()
};

const widget = new ProcmsChatbotWidget({
    botUuid: 'personalized-bot-uuid',
    apiKey: 'pk_live_personalized_key',
    userId: userContext.userId,
    metadata: {
        userName: userContext.name,
        userEmail: userContext.email,
        userPlan: userContext.plan,
        pageUrl: window.location.href,
        referrer: document.referrer,
        sessionId: getSessionId()
    }
});
                </code>
            </div>

            <div class="user-info">
                <h4>User Information:</h4>
                <input type="text" id="user-name" placeholder="User Name" value="John Doe">
                <input type="email" id="user-email" placeholder="User Email" value="<EMAIL>">
                <input type="text" id="user-plan" placeholder="User Plan" value="Premium">
                <button class="btn" onclick="updateUserContext()">Update Context</button>
            </div>

            <div id="personalized-widget" class="widget-container"></div>
            <button class="btn" onclick="loadPersonalizedWidget()">Load Personalized Widget</button>
            <button class="btn secondary" onclick="unloadPersonalizedWidget()">Unload</button>
            <div id="status-4" class="status" style="display: none;"></div>
        </div>

        <!-- Example 5: A/B Testing -->
        <div class="example">
            <h3>5. A/B Testing Implementation</h3>
            <p>Test different bot configurations and track performance.</p>
            
            <div class="code-block">
                <code>
// A/B Test Configuration
const abTestVariant = Math.random() < 0.5 ? 'A' : 'B';

const variants = {
    A: {
        botUuid: 'bot-variant-a',
        theme: 'light',
        position: 'bottom-right',
        autoOpen: false,
        greetingMessage: 'Hi! How can I help you today?'
    },
    B: {
        botUuid: 'bot-variant-b',
        theme: 'dark',
        position: 'bottom-left',
        autoOpen: true,
        greetingMessage: 'Welcome! I\'m here to assist you.'
    }
};

const widget = await ProcmsChatbot.createFloatingWidget({
    ...variants[abTestVariant],
    apiKey: 'pk_live_ab_test_key',
    metadata: { 
        abTestVariant,
        testId: 'homepage-greeting-test-v1'
    }
});

// Track variant assignment
gtag('event', 'ab_test_assignment', {
    test_name: 'homepage-greeting-test-v1',
    variant: abTestVariant
});
                </code>
            </div>

            <div>
                <p>Current Variant: <span id="current-variant">Not assigned</span></p>
                <button class="btn" onclick="runABTest()">Run A/B Test</button>
                <button class="btn secondary" onclick="stopABTest()">Stop Test</button>
            </div>
            <div id="status-5" class="status" style="display: none;"></div>
        </div>

        <!-- Example 6: Conditional Loading -->
        <div class="example">
            <h3>6. Conditional Loading</h3>
            <p>Load different bots based on page, user type, or other conditions.</p>
            
            <div class="code-block">
                <code>
// Page-based bot selection
const pageBots = {
    '/': 'general-bot-uuid',
    '/support': 'support-bot-uuid',
    '/pricing': 'sales-bot-uuid',
    '/docs': 'docs-bot-uuid'
};

// User type-based bot selection
const userTypeBots = {
    'free': 'free-user-bot-uuid',
    'premium': 'premium-bot-uuid',
    'enterprise': 'enterprise-bot-uuid'
};

// Time-based loading
const businessHours = isBusinessHours();
const botUuid = businessHours ? 'live-support-bot' : 'after-hours-bot';

// Geo-based loading
const userCountry = await getUserCountry();
const localizedBot = getLocalizedBot(userCountry);

function loadConditionalBot() {
    const currentPage = window.location.pathname;
    const userType = getUserType();
    
    let selectedBot = pageBots[currentPage] || pageBots['/'];
    
    // Override with user type if premium/enterprise
    if (userType !== 'free') {
        selectedBot = userTypeBots[userType];
    }
    
    return ProcmsChatbot.createFloatingWidget({
        botUuid: selectedBot,
        apiKey: 'pk_live_conditional_key',
        metadata: {
            page: currentPage,
            userType: userType,
            loadTime: new Date().toISOString()
        }
    });
}
                </code>
            </div>

            <div>
                <p>Page: <span id="current-page">/</span></p>
                <p>User Type: 
                    <select id="user-type-select">
                        <option value="free">Free</option>
                        <option value="premium">Premium</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </p>
                <button class="btn" onclick="loadConditionalBot()">Load Conditional Bot</button>
                <button class="btn secondary" onclick="stopConditionalBot()">Stop Bot</button>
            </div>
            <div id="status-6" class="status" style="display: none;"></div>
        </div>

        <!-- Example 7: Performance Monitoring -->
        <div class="example">
            <h3>7. Performance Monitoring</h3>
            <p>Monitor widget performance and loading times.</p>
            
            <div class="code-block">
                <code>
// Performance monitoring
const performanceMonitor = {
    startTime: performance.now(),
    
    trackLoadTime: function() {
        const loadTime = performance.now() - this.startTime;
        console.log(`Widget loaded in ${loadTime.toFixed(2)}ms`);
        
        // Send to analytics
        gtag('event', 'widget_performance', {
            load_time: Math.round(loadTime),
            user_agent: navigator.userAgent,
            connection: navigator.connection?.effectiveType
        });
    },
    
    trackError: function(error) {
        gtag('event', 'widget_error', {
            error_message: error.message,
            error_stack: error.stack,
            user_agent: navigator.userAgent
        });
    }
};

const widget = new ProcmsChatbotWidget({
    botUuid: 'performance-bot-uuid',
    apiKey: 'pk_live_performance_key',
    onReady: () => performanceMonitor.trackLoadTime(),
    onError: (error) => performanceMonitor.trackError(error)
});
                </code>
            </div>

            <div id="performance-widget" class="widget-container"></div>
            <button class="btn" onclick="loadPerformanceWidget()">Load Performance Widget</button>
            <button class="btn secondary" onclick="unloadPerformanceWidget()">Unload</button>
            
            <h4>Performance Metrics:</h4>
            <div id="performance-log" class="analytics-panel">
                Performance metrics will appear here...
            </div>
            <div id="status-7" class="status" style="display: none;"></div>
        </div>
    </div>

    <!-- Load the widget library -->
    <script src="../../../dist/widget/procms-chatbot.umd.js"></script>
    
    <script>
        // Demo configuration
        const DEMO_CONFIG = {
            botUuid: 'demo-bot-uuid-123',
            apiKey: 'pk_test_demo_api_key_12345678901234567890',
            theme: 'light',
            showHeader: true,
            showAvatar: true
        };

        let widgets = {};
        let abTestWidget = null;
        let conditionalWidget = null;

        // Helper functions
        function showStatus(id, message, type = 'info') {
            const statusEl = document.getElementById(`status-${id}`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        function logAnalytics(event, data) {
            const logEl = document.getElementById('analytics-log');
            if (logEl) {
                const timestamp = new Date().toLocaleTimeString();
                logEl.innerHTML += `[${timestamp}] ${event}: ${JSON.stringify(data)}\n`;
                logEl.scrollTop = logEl.scrollHeight;
            }
        }

        function logPerformance(metric, value) {
            const logEl = document.getElementById('performance-log');
            if (logEl) {
                const timestamp = new Date().toLocaleTimeString();
                logEl.innerHTML += `[${timestamp}] ${metric}: ${value}\n`;
                logEl.scrollTop = logEl.scrollHeight;
            }
        }

        // Example 1: Multiple Bots
        async function loadSupportBot() {
            try {
                const widget = await ProcmsChatbot.create({
                    ...DEMO_CONFIG,
                    botUuid: 'support-bot-uuid',
                    theme: 'light'
                }, '#support-chat');
                widgets.support = widget;
                showStatus(1, 'Support bot loaded successfully!', 'success');
            } catch (error) {
                showStatus(1, `Error: ${error.message}`, 'error');
            }
        }

        async function loadSalesBot() {
            try {
                const widget = await ProcmsChatbot.create({
                    ...DEMO_CONFIG,
                    botUuid: 'sales-bot-uuid',
                    theme: 'dark'
                }, '#sales-chat');
                widgets.sales = widget;
                showStatus(1, 'Sales bot loaded successfully!', 'success');
            } catch (error) {
                showStatus(1, `Error: ${error.message}`, 'error');
            }
        }

        // Example 2: Analytics Integration
        async function loadAnalyticsWidget() {
            try {
                const widget = new ProcmsChatbotWidget({
                    ...DEMO_CONFIG,
                    onMessage: (message) => {
                        logAnalytics('message', {
                            type: message.type,
                            conversationId: message.conversationId
                        });
                    },
                    onReady: () => {
                        logAnalytics('widget_ready', { timestamp: new Date().toISOString() });
                    }
                });

                widget.on('conversation-started', (id) => {
                    logAnalytics('conversation_started', { conversationId: id });
                });

                await widget.mount('#analytics-widget');
                widgets.analytics = widget;
                showStatus(2, 'Analytics widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(2, `Error: ${error.message}`, 'error');
            }
        }

        function unloadAnalyticsWidget() {
            if (widgets.analytics) {
                widgets.analytics.unmount();
                delete widgets.analytics;
                showStatus(2, 'Analytics widget unloaded.', 'info');
            }
        }

        // Example 3: Theme Switching
        async function loadThemeWidget() {
            try {
                const widget = new ProcmsChatbotWidget(DEMO_CONFIG);
                await widget.mount('#theme-widget');
                widgets.theme = widget;
                
                // Load saved theme
                const savedTheme = localStorage.getItem('chatbot-theme') || 'light';
                document.getElementById('theme-select').value = savedTheme;
                switchWidgetTheme();
                
                showStatus(3, 'Theme widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(3, `Error: ${error.message}`, 'error');
            }
        }

        function switchWidgetTheme() {
            const theme = document.getElementById('theme-select').value;
            if (widgets.theme) {
                if (theme === 'custom') {
                    widgets.theme.setCustomTheme({
                        primaryColor: '#ff6b6b',
                        backgroundColor: '#f8f9fa',
                        textPrimary: '#2d3436'
                    });
                } else {
                    widgets.theme.setTheme(theme);
                }
                localStorage.setItem('chatbot-theme', theme);
            }
        }

        function unloadThemeWidget() {
            if (widgets.theme) {
                widgets.theme.unmount();
                delete widgets.theme;
                showStatus(3, 'Theme widget unloaded.', 'info');
            }
        }

        // Example 4: Personalization
        async function loadPersonalizedWidget() {
            try {
                const userContext = getUserContext();
                const widget = new ProcmsChatbotWidget({
                    ...DEMO_CONFIG,
                    userId: userContext.userId,
                    metadata: userContext
                });

                await widget.mount('#personalized-widget');
                widgets.personalized = widget;
                showStatus(4, 'Personalized widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(4, `Error: ${error.message}`, 'error');
            }
        }

        function getUserContext() {
            return {
                userId: 'user-123',
                userName: document.getElementById('user-name').value,
                userEmail: document.getElementById('user-email').value,
                userPlan: document.getElementById('user-plan').value,
                pageUrl: window.location.href
            };
        }

        function updateUserContext() {
            if (widgets.personalized) {
                const context = getUserContext();
                widgets.personalized.updateConfig({ metadata: context });
                showStatus(4, 'User context updated!', 'success');
            }
        }

        function unloadPersonalizedWidget() {
            if (widgets.personalized) {
                widgets.personalized.unmount();
                delete widgets.personalized;
                showStatus(4, 'Personalized widget unloaded.', 'info');
            }
        }

        // Example 5: A/B Testing
        async function runABTest() {
            try {
                const variant = Math.random() < 0.5 ? 'A' : 'B';
                document.getElementById('current-variant').textContent = variant;

                const variants = {
                    A: { theme: 'light', position: 'bottom-right' },
                    B: { theme: 'dark', position: 'bottom-left' }
                };

                abTestWidget = await ProcmsChatbot.createFloatingWidget({
                    ...DEMO_CONFIG,
                    ...variants[variant],
                    metadata: { abTestVariant: variant }
                });

                showStatus(5, `A/B Test started with variant ${variant}!`, 'success');
            } catch (error) {
                showStatus(5, `Error: ${error.message}`, 'error');
            }
        }

        function stopABTest() {
            if (abTestWidget) {
                abTestWidget.unmount();
                const container = document.getElementById('procms-floating-widget');
                if (container) container.remove();
                abTestWidget = null;
                document.getElementById('current-variant').textContent = 'Not assigned';
                showStatus(5, 'A/B Test stopped.', 'info');
            }
        }

        // Example 6: Conditional Loading
        async function loadConditionalBot() {
            try {
                const userType = document.getElementById('user-type-select').value;
                const page = window.location.pathname;
                
                const botConfigs = {
                    free: { theme: 'light', botUuid: 'free-bot-uuid' },
                    premium: { theme: 'dark', botUuid: 'premium-bot-uuid' },
                    enterprise: { theme: 'auto', botUuid: 'enterprise-bot-uuid' }
                };

                conditionalWidget = await ProcmsChatbot.createFloatingWidget({
                    ...DEMO_CONFIG,
                    ...botConfigs[userType],
                    metadata: { userType, page }
                });

                showStatus(6, `Conditional bot loaded for ${userType} user!`, 'success');
            } catch (error) {
                showStatus(6, `Error: ${error.message}`, 'error');
            }
        }

        function stopConditionalBot() {
            if (conditionalWidget) {
                conditionalWidget.unmount();
                const container = document.getElementById('procms-floating-widget');
                if (container) container.remove();
                conditionalWidget = null;
                showStatus(6, 'Conditional bot stopped.', 'info');
            }
        }

        // Example 7: Performance Monitoring
        async function loadPerformanceWidget() {
            try {
                const startTime = performance.now();
                
                const widget = new ProcmsChatbotWidget({
                    ...DEMO_CONFIG,
                    onReady: () => {
                        const loadTime = performance.now() - startTime;
                        logPerformance('Load Time', `${loadTime.toFixed(2)}ms`);
                        logPerformance('Memory Usage', `${(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
                    },
                    onError: (error) => {
                        logPerformance('Error', error.message);
                    }
                });

                await widget.mount('#performance-widget');
                widgets.performance = widget;
                showStatus(7, 'Performance widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(7, `Error: ${error.message}`, 'error');
                logPerformance('Load Error', error.message);
            }
        }

        function unloadPerformanceWidget() {
            if (widgets.performance) {
                widgets.performance.unmount();
                delete widgets.performance;
                showStatus(7, 'Performance widget unloaded.', 'info');
            }
        }

        // Initialize page
        document.getElementById('current-page').textContent = window.location.pathname;
        
        console.log('Advanced integration examples loaded');
    </script>
</body>
</html>
