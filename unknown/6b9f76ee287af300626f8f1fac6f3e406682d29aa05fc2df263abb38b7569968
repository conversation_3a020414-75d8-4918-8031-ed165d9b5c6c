# ProcMS Chatbot Widget

A powerful, customizable chatbot widget that can be easily integrated into any website. Built with Vue 3, TypeScript, and modern web technologies.

## 🚀 Quick Start

### CDN Integration (Recommended)

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Website</title>
</head>
<body>
    <!-- Your website content -->
    
    <!-- ProcMS Chatbot Widget -->
    <script>
        window.PROCMS_CHATBOT_CONFIG = {
            botUuid: 'your-bot-uuid-here',
            apiKey: 'pk_live_your_api_key_here',
            theme: 'light',
            position: 'bottom-right'
        };
    </script>
    <script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"></script>
</body>
</html>
```

### NPM Installation

```bash
npm install @procms/chatbot-widget
```

```javascript
import { ProcmsChatbot } from '@procms/chatbot-widget';
import '@procms/chatbot-widget/dist/style.css';

const widget = await Procms<PERSON>hatbot.create({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here'
}, '#chatbot-container');
```

## 📋 Configuration Options

### Basic Configuration

```javascript
const config = {
    // Required
    botUuid: 'your-bot-uuid-here',        // Your bot's unique identifier
    apiKey: 'pk_live_your_api_key_here',  // Your API key
    
    // Optional
    userId: 'user-123',                   // User identifier for personalization
    theme: 'light',                       // 'light', 'dark', 'auto'
    position: 'bottom-right',             // Widget position
    autoOpen: false,                      // Auto-open on page load
    showHeader: true,                     // Show header with bot info
    showAvatar: true,                     // Show bot avatar
    
    // Dimensions
    width: 400,                           // Widget width in pixels
    height: 600,                          // Widget height in pixels
    
    // Advanced
    mode: 'auto',                         // 'widget', 'iframe', 'auto'
    customCSS: '',                        // Custom CSS styles
    
    // Events
    onReady: () => console.log('Widget ready'),
    onMessage: (msg) => console.log('New message:', msg),
    onError: (err) => console.error('Error:', err)
};
```

### Theme Configuration

```javascript
// Built-in themes
const widget = new ProcmsChatbotWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    theme: 'dark'  // 'light', 'dark', 'auto'
});

// Custom theme
widget.setCustomTheme({
    primaryColor: '#ff6b6b',
    backgroundColor: '#f8f9fa',
    textPrimary: '#2d3436',
    borderRadius: '12px',
    fontFamily: 'Inter, sans-serif'
});
```

## 🎯 Integration Methods

### 1. Floating Widget (Most Common)

```javascript
// Creates a floating chat button
const floatingWidget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    position: 'bottom-right',
    autoOpen: false
});
```

### 2. Embedded Widget

```html
<div id="chatbot-container" style="width: 400px; height: 600px;"></div>

<script>
const widget = await ProcmsChatbot.create({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx'
}, '#chatbot-container');
</script>
```

### 3. Iframe Integration

```javascript
// Generate iframe code
const iframeCode = ProcmsChatbot.generateIframeCode({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    width: 400,
    height: 600
});

document.getElementById('container').innerHTML = iframeCode;
```

### 4. Auto-Detection Mode

```javascript
// Automatically chooses best integration method
const widget = new ProcmsChatbotWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    mode: 'auto'  // Detects conflicts and chooses widget or iframe
});

// Get detection report
const report = ProcmsChatbot.getDetectionReport(config);
console.log('Recommended mode:', report.recommendedMode);
```

## 🎨 Customization

### CSS Customization

```css
/* Override widget styles */
.procms-chatbot-widget {
    --procms-primary: #your-brand-color;
    --procms-widget-border-radius: 16px;
    --procms-font-family: 'Your Font', sans-serif;
}

/* Custom header styling */
.procms-widget-header {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

/* Custom message styling */
.procms-message--user .procms-message__content {
    background: #your-user-message-color;
}
```

### JavaScript Customization

```javascript
const widget = new ProcmsChatbotWidget(config);

// Add custom event listeners
widget.on('message', (message) => {
    // Track message in analytics
    gtag('event', 'chatbot_message', {
        message_type: message.type,
        conversation_id: message.conversationId
    });
});

widget.on('conversation-started', (conversationId) => {
    // Track conversation start
    console.log('Conversation started:', conversationId);
});

// Custom theme based on user preferences
const userTheme = localStorage.getItem('user-theme');
if (userTheme) {
    widget.setTheme(userTheme);
}
```

## 📡 API Reference

### Widget Class Methods

```javascript
const widget = new ProcmsChatbotWidget(config);

// Lifecycle
await widget.mount('#container');     // Mount widget to element
widget.unmount();                     // Unmount and cleanup
widget.destroy();                     // Complete cleanup

// State
widget.isMounted();                   // Check if mounted
widget.getMode();                     // Get current mode ('widget'|'iframe')
widget.getBotConfig();                // Get bot configuration

// Configuration
widget.updateConfig({ theme: 'dark' }); // Update configuration
widget.setTheme('dark');              // Set theme
widget.setCustomTheme(themeConfig);   // Apply custom theme

// Events
widget.on(event, callback);           // Add event listener
widget.off(event, callback);          // Remove event listener
widget.once(event, callback);         // One-time event listener
```

### Static Methods

```javascript
// Create and mount in one call
const widget = await ProcmsChatbot.create(config, selector);

// Create floating widget
const floating = await ProcmsChatbot.createFloatingWidget(config);

// Generate iframe embed code
const iframe = ProcmsChatbot.generateIframeCode(config);

// Auto-initialize widget
const auto = await ProcmsChatbot.init(config);

// Detect best mode
const mode = ProcmsChatbot.detectMode(config);

// Get detection report
const report = ProcmsChatbot.getDetectionReport(config);
```

### Events

```javascript
widget.on('ready', () => {
    console.log('Widget is ready');
});

widget.on('mounted', ({ mode, botConfig }) => {
    console.log('Widget mounted in mode:', mode);
});

widget.on('message', (message) => {
    console.log('New message:', message);
});

widget.on('conversation-started', (conversationId) => {
    console.log('Conversation started:', conversationId);
});

widget.on('conversation-ended', (conversationId) => {
    console.log('Conversation ended:', conversationId);
});

widget.on('error', (error) => {
    console.error('Widget error:', error);
});

widget.on('bot-loaded', (botConfig) => {
    console.log('Bot configuration loaded:', botConfig);
});
```

## 🔧 Advanced Usage

### Multiple Widgets

```javascript
// Multiple widgets on same page
const supportWidget = await ProcmsChatbot.create({
    botUuid: 'support-bot-uuid',
    apiKey: 'pk_live_support_key'
}, '#support-chat');

const salesWidget = await ProcmsChatbot.create({
    botUuid: 'sales-bot-uuid',
    apiKey: 'pk_live_sales_key'
}, '#sales-chat');
```

### Conditional Loading

```javascript
// Load widget based on conditions
if (userNeedsSupport()) {
    const widget = await ProcmsChatbot.createFloatingWidget({
        botUuid: 'support-bot',
        apiKey: 'pk_live_xxx',
        autoOpen: true
    });
}

// Load different bots for different pages
const botConfig = {
    '/support': 'support-bot-uuid',
    '/sales': 'sales-bot-uuid',
    '/': 'general-bot-uuid'
};

const botUuid = botConfig[window.location.pathname] || botConfig['/'];
```

### Integration with Analytics

```javascript
const widget = new ProcmsChatbotWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    onMessage: (message) => {
        // Google Analytics
        gtag('event', 'chatbot_interaction', {
            message_type: message.type,
            conversation_id: message.conversationId
        });
        
        // Custom analytics
        analytics.track('Chatbot Message', {
            type: message.type,
            content: message.content,
            timestamp: message.timestamp
        });
    },
    onConversationStarted: (conversationId) => {
        gtag('event', 'chatbot_conversation_start', {
            conversation_id: conversationId
        });
    }
});
```

### A/B Testing

```javascript
// A/B test different bot configurations
const variant = Math.random() < 0.5 ? 'A' : 'B';

const config = {
    A: {
        botUuid: 'bot-variant-a',
        theme: 'light',
        position: 'bottom-right'
    },
    B: {
        botUuid: 'bot-variant-b',
        theme: 'dark',
        position: 'bottom-left'
    }
};

const widget = await ProcmsChatbot.createFloatingWidget({
    ...config[variant],
    apiKey: 'pk_live_xxx',
    metadata: { variant }
});
```

## 🛠️ Troubleshooting

### Common Issues

#### Widget Not Loading
```javascript
// Check console for errors
console.log('Widget library loaded:', typeof ProcmsChatbotWidget !== 'undefined');

// Verify configuration
const config = {
    botUuid: 'your-bot-uuid',  // Check this is correct
    apiKey: 'pk_live_xxx'      // Check API key format
};

// Test API connectivity
fetch('/api/public/bots/your-bot-uuid/config', {
    headers: { 'Authorization': 'Bearer pk_live_xxx' }
}).then(response => console.log('API Status:', response.status));
```

#### CSS Conflicts
```css
/* Increase specificity if needed */
.procms-chatbot-widget.procms-chatbot-widget {
    /* Your styles with higher specificity */
}

/* Or use !important sparingly */
.procms-chatbot-widget {
    font-family: 'Your Font' !important;
}
```

#### JavaScript Conflicts
```javascript
// Force iframe mode to avoid conflicts
const widget = new ProcmsChatbotWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    mode: 'iframe'  // Forces iframe isolation
});

// Or check detection report
const report = ProcmsChatbot.getDetectionReport(config);
console.log('Conflicts detected:', report.conflicts);
```

### Debug Mode

```javascript
// Enable debug logging
window.PROCMS_DEBUG = true;

// Get detailed widget information
const widget = new ProcmsChatbotWidget(config);
console.log('Widget config:', widget.getConfig());
console.log('Widget mode:', widget.getMode());
console.log('Bot config:', widget.getBotConfig());
```

## 📱 Mobile Optimization

### Responsive Design
The widget automatically adapts to mobile devices with:
- Touch-friendly interface (48px minimum touch targets)
- Optimized font sizes (16px minimum to prevent zoom)
- Full-screen mode on small devices
- Swipe gestures support

### Mobile-Specific Configuration
```javascript
const isMobile = window.innerWidth <= 768;

const widget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    width: isMobile ? '100vw' : 400,
    height: isMobile ? '100vh' : 600,
    position: isMobile ? 'center' : 'bottom-right'
});
```

## 🔒 Security

### API Key Security
- Never expose API keys in client-side code for production
- Use environment variables or server-side rendering
- Rotate API keys regularly
- Monitor API key usage

### Content Security Policy
```html
<!-- Add to your CSP if needed -->
<meta http-equiv="Content-Security-Policy" 
      content="script-src 'self' https://cdn.procms.com; 
               connect-src 'self' https://api.procms.com;">
```

### Domain Restrictions
Configure allowed domains in your bot settings to prevent unauthorized usage.

## 📊 Performance

### Bundle Size
- UMD build: ~33KB gzipped
- ES build: ~0.2KB (imports only what's needed)
- CSS: ~7KB gzipped

### Optimization Tips
1. Use iframe mode for better isolation
2. Load widget asynchronously
3. Implement lazy loading for non-critical pages
4. Use CDN for faster delivery
5. Enable compression on your server

## 🆕 What's New

### Version 2.0.0
- ✅ Hybrid widget/iframe architecture
- ✅ Enhanced theme system
- ✅ Improved API client with retry logic
- ✅ Better mobile support
- ✅ Accessibility improvements
- ✅ TypeScript support

### Migration from v1.x
See [Migration Guide](./migration.md) for detailed upgrade instructions.

## 📚 Documentation

- 📖 [API Reference](./api-reference.md) - Complete API documentation
- 🔧 [Integration Examples](./examples/) - Framework-specific examples
- 🚀 [Migration Guide](./migration.md) - Upgrade from older versions
- 🛠️ [Troubleshooting](./troubleshooting.md) - Common issues and solutions
- 🎨 [Theme System](../widget/styles/README.md) - CSS and theming guide

## 📞 Support

- 📖 [Full Documentation](https://docs.procms.com/widget)
- 🐛 [Report Issues](https://github.com/procms/widget/issues)
- 💬 [Community Forum](https://community.procms.com)
- 📧 [Email Support](mailto:<EMAIL>)

## 📄 License

MIT License - see [LICENSE](./LICENSE) file for details.
