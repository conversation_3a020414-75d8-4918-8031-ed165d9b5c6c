/**
 * Simple Event Emitter for Widget
 */

export class EventEmitter {
  private listeners: { [key: string]: Function[] } = {};

  /**
   * Add event listener
   */
  on(event: string, callback: Function): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback?: Function): void {
    if (!this.listeners[event]) return;

    if (callback) {
      this.listeners[event] = this.listeners[event].filter(
        cb => cb !== callback
      );
    } else {
      this.listeners[event] = [];
    }
  }

  /**
   * Emit event
   */
  emit(event: string, data?: any): void {
    if (!this.listeners[event]) return;

    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }

  /**
   * Add one-time event listener
   */
  once(event: string, callback: Function): void {
    const onceCallback = (data: any) => {
      callback(data);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }

  /**
   * Remove all listeners
   */
  removeAllListeners(event?: string): void {
    if (event) {
      delete this.listeners[event];
    } else {
      this.listeners = {};
    }
  }

  /**
   * Get listener count for event
   */
  listenerCount(event: string): number {
    return this.listeners[event]?.length || 0;
  }

  /**
   * Get all event names
   */
  eventNames(): string[] {
    return Object.keys(this.listeners);
  }
}
