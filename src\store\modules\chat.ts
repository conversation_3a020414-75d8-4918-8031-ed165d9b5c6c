import { defineStore } from "pinia";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import { getChatBots } from "@/views/chat/utils/auth-api";
import type { ChatBot, Conversation } from "@/views/chat/utils/type";

export type ChatBotState = {
  loading: boolean;
  bots: ChatBot[];
  filters: Record<string, any>;
  selectedBot: ChatBot | null;
  selectedConversation: Conversation | null;
};

export const useChatBotStore = defineStore("chat-management", {
  state: (): ChatBotState => {
    return {
      loading: false,
      bots: [],
      filters: {
        searchQuery: ""
      },
      selectedBot: null,
      selectedConversation: {}
    };
  },

  actions: {
    // Fetch bots from API
    async getChatBots() {
      this.loading = true;
      try {
        const { data, success } = await getChatBots();
        if (success) {
          this.bots = useConvertKeyToCamel(data || []);
        }
      } catch (e: any) {
        console.info(e);
      } finally {
        this.loading = false;
      }
    },
    setConversation(conversation: any) {
      this.selectedConversation = conversation;
    }
  }
});

export function useChatBotStoreHook() {
  return useChatBotStore();
}
