<template>
  <div
    ref="widgetContainer"
    class="procms-chatbot-widget"
    :class="widgetClasses"
    :data-procms-theme="config.theme"
  >
    <!-- Header -->
    <div v-if="config.showHeader" class="procms-widget-header">
      <div class="procms-widget-header__content">
        <img
          v-if="config.showAvatar && botConfig.logoUrl"
          :src="botConfig.logoUrl"
          :alt="botConfig.name"
          class="procms-widget-header__avatar"
        />
        <div class="procms-widget-header__info">
          <h3 class="procms-widget-header__title">{{ botConfig.name }}</h3>
          <span class="procms-widget-header__status">Online</span>
        </div>
      </div>
      <div class="procms-widget-header__actions">
        <button class="procms-widget-header__action" @click="closeWidget">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Messages Container -->
    <div class="procms-widget-body">
      <div ref="messagesContainer" class="procms-messages-container">
        <!-- Greeting Message -->
        <div
          v-if="botConfig.greetingMessage"
          class="procms-message procms-message--bot"
        >
          <img
            v-if="config.showAvatar && botConfig.logoUrl"
            :src="botConfig.logoUrl"
            :alt="botConfig.name"
            class="procms-message__avatar"
          />
          <div class="procms-message__content">
            <div class="procms-message__text">
              {{ botConfig.greetingMessage }}
            </div>
            <div class="procms-message__meta">
              <span class="procms-message__time">{{
                formatTime(new Date())
              }}</span>
            </div>
          </div>
        </div>

        <!-- Starter Messages -->
        <div
          v-if="showStarterMessages && botConfig.starterMessages?.length"
          class="procms-starter-messages"
        >
          <button
            v-for="(message, index) in botConfig.starterMessages"
            :key="index"
            class="procms-starter-messages__button"
            @click="sendStarterMessage(message)"
          >
            {{ message }}
          </button>
        </div>

        <!-- Chat Messages -->
        <div
          v-for="message in messages"
          :key="message.id"
          class="procms-message"
          :class="{
            'procms-message--user': message.type === 'user',
            'procms-message--bot': message.type === 'bot'
          }"
        >
          <img
            v-if="
              message.type === 'bot' && config.showAvatar && botConfig.logoUrl
            "
            :src="botConfig.logoUrl"
            :alt="botConfig.name"
            class="procms-message__avatar"
          />
          <div class="procms-message__content">
            <div class="procms-message__text">{{ message.content }}</div>
            <div class="procms-message__meta">
              <span class="procms-message__time">{{
                formatTime(message.timestamp)
              }}</span>
            </div>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div v-if="isTyping" class="procms-typing-indicator">
          <img
            v-if="config.showAvatar && botConfig.logoUrl"
            :src="botConfig.logoUrl"
            :alt="botConfig.name"
            class="procms-typing-indicator__avatar"
          />
          <div class="procms-typing-indicator__content">
            <div class="procms-typing-indicator__dots">
              <span />
              <span />
              <span />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="procms-widget-footer">
      <div class="procms-input-area">
        <div class="procms-input-area__container">
          <input
            ref="messageInput"
            v-model="inputMessage"
            :placeholder="inputPlaceholder"
            :disabled="isLoading"
            class="procms-input-area__input"
            @keypress.enter="sendMessage"
            @input="handleInput"
          />
        </div>
        <button
          :disabled="!inputMessage.trim() || isLoading"
          class="procms-input-area__send"
          @click="sendMessage"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="errorMessage" class="procms-widget-error">
      <div class="procms-error-icon">⚠️</div>
      <div class="procms-error-title">Error</div>
      <div class="procms-error-message">{{ errorMessage }}</div>
      <button class="procms-error-retry" @click="clearError">Dismiss</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import type { WidgetConfig, BotConfig, ChatMessage } from "../types";
import { getBotApiClient } from "../api/bot-api";
import { ThemeManager } from "../styles/theme-manager";

// Props
interface Props {
  config: WidgetConfig;
  botConfig: BotConfig;
  onMessage?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
  onOpen?: () => void;
  onReady?: () => void;
}

const props = defineProps<Props>();

// Reactive data
const messages = ref<ChatMessage[]>([]);
const inputMessage = ref("");
const isLoading = ref(false);
const isTyping = ref(false);
const errorMessage = ref("");
const showStarterMessages = ref(true);
const conversationId = ref<string | null>(null);

// Refs
const messagesContainer = ref<HTMLElement>();
const messageInput = ref<HTMLInputElement>();
const widgetContainer = ref<HTMLElement>();

// Theme manager
let themeManager: ThemeManager | null = null;

// Computed
const widgetClasses = computed(() => ({
  [`theme-${props.config.theme}`]: true,
  [`position-${props.config.position}`]: true,
  "has-error": !!errorMessage.value
}));

// Remove widgetStyles as we'll use theme manager instead

const inputPlaceholder = computed(() => {
  return isLoading.value ? "Sending..." : "Type your message...";
});

// Methods
const sendMessage = async () => {
  const message = inputMessage.value.trim();
  if (!message || isLoading.value) return;

  try {
    isLoading.value = true;
    showStarterMessages.value = false;

    // Add user message
    const userMessage: ChatMessage = {
      id: generateId(),
      conversationId: conversationId.value || "",
      type: "user",
      content: message,
      timestamp: new Date()
    };
    messages.value.push(userMessage);
    inputMessage.value = "";

    // Scroll to bottom
    await nextTick();
    scrollToBottom();

    // Show typing indicator
    isTyping.value = true;

    // Send to API
    const apiClient = getBotApiClient(props.config.apiKey);

    // Start conversation if needed
    if (!conversationId.value) {
      const convResponse = await apiClient.startConversation(
        props.config.botUuid,
        props.config.userId
      );
      if (convResponse.success && convResponse.data) {
        conversationId.value = convResponse.data.conversationId;
      }
    }

    // Send message
    const response = await apiClient.sendMessage(
      props.config.botUuid,
      conversationId.value!,
      message
    );

    isTyping.value = false;

    if (response.success && response.data) {
      // Add bot response
      const botMessage: ChatMessage = {
        id: response.data.messageId || generateId(),
        conversationId: conversationId.value!,
        type: "bot",
        content: response.data.response,
        timestamp: new Date(),
        metadata: {
          tokens: response.data.tokens
        }
      };
      messages.value.push(botMessage);

      // Emit message event
      props.onMessage?.(botMessage);
    } else {
      throw new Error(response.message || "Failed to send message");
    }
  } catch (error) {
    isTyping.value = false;
    console.error("Failed to send message:", error);
    errorMessage.value =
      error instanceof Error ? error.message : "Failed to send message";
    props.onError?.(error);
  } finally {
    isLoading.value = false;
    await nextTick();
    scrollToBottom();
  }
};

const sendStarterMessage = (message: string) => {
  inputMessage.value = message;
  sendMessage();
};

const handleInput = () => {
  // Handle typing events if needed
};

const closeWidget = () => {
  props.onClose?.();
};

const clearError = () => {
  errorMessage.value = "";
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
};

const generateId = (): string => {
  return Math.random().toString(36).substring(2, 11);
};

// Lifecycle
onMounted(() => {
  // Initialize theme manager
  if (widgetContainer.value) {
    themeManager = new ThemeManager(widgetContainer.value);

    // Apply bot theme if available
    if (props.botConfig.theme) {
      themeManager.applyBotTheme(props.botConfig.theme);
    }

    // Set theme mode
    if (props.config.theme) {
      themeManager.setTheme(props.config.theme);
    }
  }

  // Focus input
  messageInput.value?.focus();

  // Emit ready event
  props.onReady?.();
});

onUnmounted(() => {
  // Cleanup theme manager
  if (themeManager) {
    themeManager.destroy();
    themeManager = null;
  }
});

// Watch for new messages to scroll
watch(
  messages,
  () => {
    nextTick(() => scrollToBottom());
  },
  { deep: true }
);
</script>

<style>
/* Import widget styles - note: not scoped to allow CSS variables to work */
@import "../styles/widget.scss";
</style>
