#!/usr/bin/env node

/**
 * Script để kiểm tra tất cả các API endpoints đ<PERSON> đư<PERSON> cập nhật từ /api thành /api/v1
 */

const fs = require('fs');
const path = require('path');

// Danh sách các thư mục cần kiểm tra
const checkDirectories = [
  'src/api',
  'src/views',
  'src/utils/http'
];

// Pattern để tìm các API endpoints chưa được cập nhật
const apiPattern = /["'`]\/api(?!\/v1)[\w\/\-\$\{\}]*["'`]/g;

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const matches = content.match(apiPattern);
    
    if (matches && matches.length > 0) {
      console.log(`\n❌ File: ${filePath}`);
      matches.forEach((match, index) => {
        const lines = content.split('\n');
        const lineNumber = lines.findIndex(line => line.includes(match)) + 1;
        console.log(`   Line ${lineNumber}: ${match}`);
      });
      return false;
    }
    return true;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return false;
  }
}

function checkDirectory(dirPath) {
  const results = [];
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.js') || item.endsWith('.vue')) {
        const isClean = checkFile(fullPath);
        results.push({ file: fullPath, clean: isClean });
      }
    });
  }
  
  if (fs.existsSync(dirPath)) {
    walkDir(dirPath);
  } else {
    console.log(`⚠️  Directory not found: ${dirPath}`);
  }
  
  return results;
}

function main() {
  console.log('🔍 Checking API endpoints...\n');
  
  let allResults = [];
  let totalFiles = 0;
  let cleanFiles = 0;
  
  checkDirectories.forEach(dir => {
    console.log(`📁 Checking directory: ${dir}`);
    const results = checkDirectory(dir);
    allResults = allResults.concat(results);
    
    const dirCleanFiles = results.filter(r => r.clean).length;
    totalFiles += results.length;
    cleanFiles += dirCleanFiles;
    
    console.log(`   ✅ Clean files: ${dirCleanFiles}/${results.length}`);
  });
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 SUMMARY:`);
  console.log(`   Total files checked: ${totalFiles}`);
  console.log(`   Clean files: ${cleanFiles}`);
  console.log(`   Files with issues: ${totalFiles - cleanFiles}`);
  
  if (cleanFiles === totalFiles) {
    console.log('\n🎉 All API endpoints have been successfully updated to /api/v1!');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some files still contain /api endpoints that need to be updated.');
    console.log('Please review the files listed above and update them manually.');
    process.exit(1);
  }
}

// Chạy script
main();
