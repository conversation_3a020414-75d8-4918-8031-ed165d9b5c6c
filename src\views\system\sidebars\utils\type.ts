export interface Sidebar {
  id: number;
  menuType: number; // 0: menu, 1: iframe, 2: external link, 3: button
  parentId?: number;
  title: string;
  name: string;
  path?: string;
  component?: string;
  rank: number;
  redirect?: string;
  icon?: string;
  extraIcon?: string;
  enterTransition?: string;
  leaveTransition?: string;
  activePath?: string;
  auths?: string[];
  frameSrc?: string;
  frameLoading?: boolean;
  keepAlive?: boolean;
  hiddenTag?: boolean;
  fixedTag?: boolean;
  showLink?: boolean;
  showParent?: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

export interface FormItemProps {
  id?: number | null;
  menuType?: number; // 0: menu, 1: iframe, 2: external link, 3: button
  parentId?: number;
  title?: string;
  name?: string;
  path?: string;
  component?: string;
  rank?: number;
  redirect?: string;
  icon?: string;
  extraIcon?: string;
  enterTransition?: string;
  leaveTransition?: string;
  activePath?: string;
  auths?: string[];
  frameSrc?: string;
  frameLoading?: boolean;
  keepAlive?: boolean;
  hiddenTag?: boolean;
  fixedTag?: boolean;
  showLink?: boolean;
  showParent?: boolean;
  // Legacy fields for backward compatibility
  meta?: any;
  parent_id?: number;
  sort?: number;
  is_hidden?: boolean;
  is_keepalive?: boolean;
  is_affix?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface SidebarFilterProps {
  name?: string;
  title?: string;
  menuType?: number;
  parentId?: number;
  showLink?: boolean;
  keepAlive?: boolean;
  is_hidden?: boolean; // Legacy field
  isTrashed?: boolean;
}
