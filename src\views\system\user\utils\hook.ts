import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, UserFilterProps } from "./type";
import {
  getUsers,
  createUser,
  updateUserById,
  bulkDeleteUsers,
  deleteUser,
  deleteUserPermanent,
  bulkDeleteUsersPermanent,
  restoreUser,
  bulkRestoreUsers
} from "./auth-api";
import { getRolesDropdown } from "@/views/system/role/utils/auth-api";

export function useUserHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<UserFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const rolesDropdown = ref([]);

  // Form refs
  const userFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "active"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetUsers = async () => {
    try {
      loading.value = true;
      const res = await getUsers(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching users:", error);
      message($t("Failed to fetch users"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnGetRolesDropdown = async () => {
    try {
      const res = await getRolesDropdown();
      rolesDropdown.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching roles dropdown:", error);
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = () => {
    fnGetUsers();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetUsers();
  };

  const fnHandleSortChange = async (sortBy: string, sortOrder: string) => {
    sort.value = { sortBy, sortOrder };
    await fnGetUsers();
  };

  const fnHandleCreateUser = async (data: any) => {
    try {
      loading.value = true;
      const response = await createUser(data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetUsers();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateUser = async (id: number, data: any) => {
    try {
      loading.value = true;
      const response = await updateUserById(id, data);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteUser(row.id);
      message($t("Deleted successfully"), { type: "success" });
      await fnGetUsers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting user:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteUsers({ ids: selectedIds });
      message($t("Deleted successfully"), { type: "success" });
      await fnGetUsers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting users:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteUserPermanent(row.id);
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetUsers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting user:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteUsersPermanent({ ids: selectedIds });
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetUsers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting users:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await restoreUser(row.id);
      message($t("Restored successfully"), { type: "success" });
      await fnGetUsers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error restoring user:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  const handleBulkRestore = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await bulkRestoreUsers({ ids: selectedIds });
      message($t("Restored successfully"), { type: "success" });
      await fnGetUsers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk restoring users:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  /*
   ***************************
   *   Form handlers and actions
   *   Form handlers and actions
   ***************************
   */

  const handleFilter = async (values: UserFilterProps) => {
    filterRef.value = values;
    await fnGetUsers();
  };

  const handleSubmit = async (values: FieldValues) => {
    let success = false;
    if (values.id != null) {
      success = await fnHandleUpdateUser(Number(values.id), values);
    } else {
      success = await fnHandleCreateUser(values);
      if (success) {
        drawerValues.value = { status: "active" };
        userFormRef.value?.resetForm();
      }
    }
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    rolesDropdown,
    handleBulkDelete,
    handleDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,
    fnGetUsers,
    fnGetRolesDropdown,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    userFormRef,
    handleSubmit,
    handleFilter
  };
}
