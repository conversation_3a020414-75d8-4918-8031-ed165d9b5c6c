const Layout = () => import("@/layout/index.vue");

export default {
  path: "/auth/systems",
  name: "System",
  component: Layout,
  redirect: "/auth/systems/settings",
  meta: {
    icon: "ri:settings-3-line",
    title: "System",
    rank: 8,
    // @ts-ignore
    roles: ["super-admin", "admin"]
  },
  children: [
    {
      path: "/auth/systems/settings",
      name: "SettingIndex",
      component: () => import("@/views/system/setting/index.vue"),
      meta: {
        icon: "ri:settings-4-line",
        title: "Settings",
        showLink: true
      }
    },
    {
      path: "/auth/languages",
      name: "LanguageIndex",
      component: () => import("@/views/system/language/index.vue"),
      meta: {
        icon: "ri:global-line",
        title: "Languages",
        showLink: true,
        auths: ["language:list"]
      }
    },
    {
      path: "/auth/systems/translations",
      name: "TranslationIndex",
      component: () => import("@/views/system/translation/index.vue"),
      meta: {
        icon: "ri:translate-2",
        title: "Translations",
        showLink: true
      }
    },
    {
      path: "/auth/systems/users",
      name: "UserIndex",
      component: () => import("@/views/system/user/index.vue"),
      meta: {
        icon: "ri:user-line",
        title: "Users",
        showLink: true
      }
    },
    {
      path: "/auth/systems/roles",
      name: "RoleIndex",
      component: () => import("@/views/system/role/index.vue"),
      meta: {
        icon: "ri:shield-user-line",
        title: "Roles",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
