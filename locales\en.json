{"Reload": "Reload", "Close Current Tab": "Close Current Tab", "Close Left Tabs": "Close Left Tabs", "Close Right Tabs": "Close Right Tabs", "Close Other Tabs": "Close Other Tabs", "Close All Tabs": "Close All Tabs", "Content Fullscreen": "Content Fullscreen", "Email is required": "Email is required", "Invalid email format": "Invalid email format", "Username is required": "Username is required", "Username too short (min {count})": "Username too short (min {count})", "Username too long (max {count})": "Username too long (max {count})", "Invalid username format": "Invalid username format", "Password is required": "Password is required", "Password too short (min {count})": "Password too short (min {count})", "Password needs numbers": "Password needs numbers", "Password needs symbols": "Password needs symbols", "Password needs uppercase": "Password needs uppercase", "Password needs lowercase": "Password needs lowercase", "Confirm password required": "Confirm password required", "Passwords don't match": "Passwords don't match", "OTP required": "OTP required", "OTP must be numbers only": "OTP must be numbers only", "OTP must be {count} digits": "OTP must be {count} digits", "{field} is required": "{field} is required", "{field} too short (min {count})": "{field} too short (min {count})", "{field} too long (max {count})": "{field} too long (max {count})", "{field} invalid format": "{field} invalid format", "Accept terms required": "Accept terms required", "Username/email required": "Username/email required", "Verification code required": "Verification code required", "Verification code incorrect": "Verification code incorrect", "Login": "<PERSON><PERSON>", "Register": "Register", "Password": "Password", "Email": "Email", "Username": "Username", "Username/Email": "Username/Email", "VerifyCode": "Verify Code", "Confirm Password": "Confirm Password", "First Name": "First Name", "Last Name": "Last Name", "OTP Code": "OTP Code", "New Password": "New Password", "Confirm New Password": "Confirm New Password", "Verification Code": "Verification Code", "Create Account": "Create Account", "I accept the": "I accept the", "Terms and Conditions": "Terms and Conditions", "Forgot Password": "Forgot Password", "Forgot password?": "Forgot password?", "Send OTP Code": "Send OTP Code", "Reset Password": "Reset Password", "Verify Email": "<PERSON><PERSON><PERSON>", "Change Email": "Change Email", "Back to Login": "Back to Login", "Sign In": "Sign In", "Sign Up": "Sign Up", "Enter your email address and we'll send you an OTP code to reset your password": "Enter your email address and we'll send you an OTP code to reset your password", "Enter the OTP code sent to": "Enter the OTP code sent to", "We've sent a verification code to": "We've sent a verification code to", "Didn't receive the code?": "Didn't receive the code?", "Resend OTP": "Resend OTP", "Resend Code": "Resend Code", "Resend in {seconds}s": "Resend in {seconds}s", "Or continue with": "Or continue with", "Continue with {provider}": "Continue with {provider}", "Please enter email": "Please enter email", "Please enter the password": "Please enter the password", "Use 8-18 chars with uppercase, lowercase, numbers & symbols.": "Use 8-18 chars with uppercase, lowercase, numbers & symbols.", "Router Error": "Router <PERSON>", "Some information is incorrect. Please review and try again.": "Some information is incorrect. Please review and try again.", "Social login failed": "Social login failed", "Language Management": "Language Management", "Translation Management": "Translation Management", "Page Management": "Page Management", "Selection": "Selection", "ID": "ID", "Name": "Name", "Code": "Code", "Language Code": "Language Code", "Language Name": "Language Name", "Native Name": "Native Name", "Direction": "Direction", "Flag": "Flag", "Sort Order": "Sort Order", "Is Active": "Is Active", "Is Default": "<PERSON>", "Default": "<PERSON><PERSON><PERSON>", "Active": "Active", "Inactive": "Inactive", "Yes": "Yes", "No": "No", "All": "All", "Left to Right": "Left to Right", "Right to Left": "Right to Left", "Status": "Status", "Trashed": "Trashed", "Created At": "Created At", "Operations": "Operations", "Translation Key": "Translation Key", "Translation Value": "Translation Value", "Language": "Language", "Group": "Group", "Plural": "Plural", "Is Plural": "Is Plural", "Description": "Description", "Title": "Title", "Page Title": "Page Title", "Page Slug": "<PERSON>", "Page Content": "Page Content", "Excerpt": "Excerpt", "Featured Image": "Featured Image", "Template": "Template", "Categories": "Categories", "Tags": "Tags", "Published At": "Published At", "Is Sticky": "<PERSON>", "Allow Comments": "Allow Comments", "Sticky": "<PERSON>y", "Author": "Author", "Category": "Category", "Tag": "Tag", "Visibility": "Visibility", "Draft": "Draft", "Published": "Published", "Pending": "Pending", "Private": "Private", "Public": "Public", "Password Protected": "Password Protected", "Default Template": "<PERSON><PERSON><PERSON>", "Please enter the language code": "Please enter the language code", "Please enter the language name": "Please enter the language name", "Please enter the native name": "Please enter the native name", "Please enter the translation key": "Please enter the translation key", "Please enter the translation value": "Please enter the translation value", "Please select the language": "Please select the language", "Please input language code": "Please input language code", "Please input language name": "Please input language name", "Please input native name": "Please input native name", "Please input translation key": "Please input translation key", "Please input translation value": "Please input translation value", "Please select language": "Please select language", "Please input page title": "Please input page title", "Please input page slug": "Please input page slug", "Please input page content": "Please input page content", "Please select the page status": "Please select the page status", "Please select the page visibility": "Please select the page visibility", "Length must be between 2 and 10 characters": "Length must be between 2 and 10 characters", "Length must be between 2 and 100 characters": "Length must be between 2 and 100 characters", "Length must be between 2 and 255 characters": "Length must be between 2 and 255 characters", "Translation value cannot exceed 5000 characters": "Translation value cannot exceed 5000 characters", "Key can only contain letters, numbers, dots, underscores and hyphens": "Key can only contain letters, numbers, dots, underscores and hyphens", "Group name cannot exceed 100 characters": "Group name cannot exceed 100 characters", "Slug can only contain lowercase letters, numbers and hyphens": "Slug can only contain lowercase letters, numbers and hyphens", "Meta title should not exceed 60 characters": "Meta title should not exceed 60 characters", "Meta description should not exceed 160 characters": "Meta description should not exceed 160 characters", "Meta keywords should not exceed 255 characters": "Meta keywords should not exceed 255 characters", "e.g., en, vi, fr": "e.g., en, vi, fr", "e.g., English, Vietnamese": "e.g., English, Vietnamese", "e.g., English, Tiếng Việt": "e.g., English, Tiếng Việt", "e.g., 🇺🇸, 🇻🇳": "e.g., 🇺🇸, 🇻🇳", "e.g., user.name, common.save": "e.g., user.name, common.save", "Select text direction": "Select text direction", "Select status": "Select status", "Select active status": "Select active status", "Select default status": "Select default status", "Select trashed status": "Select trashed status", "Select plural status": "Select plural status", "Select page status": "Select page status", "Select page visibility": "Select page visibility", "Select page template": "Select page template", "Select categories": "Select categories", "Select tags": "Select tags", "Select publish date": "Select publish date", "Select template": "Select template", "Select category": "Select category", "Select tag": "Select tag", "Select sticky status": "Select sticky status", "Select comment status": "Select comment status", "Select visibility": "Select visibility", "Select or enter group": "Select or enter group", "Select group": "Select group", "Enter language code": "Enter language code", "Enter language name": "Enter language name", "Enter native name": "Enter native name", "Enter translation key": "Enter translation key", "Enter translation value": "Enter translation value", "Enter description (optional)": "Enter description (optional)", "Enter page title": "Enter page title", "Enter page slug": "Enter page slug", "Enter page slug (auto-generated from title)": "Enter page slug (auto-generated from title)", "Enter page content": "Enter page content", "Enter page excerpt (optional)": "Enter page excerpt (optional)", "Enter image URL or upload": "Enter image URL or upload", "Add Language": "Add Language", "Edit Language": "Edit Language", "Filter Languages": "Filter Languages", "Add Translation": "Add Translation", "Edit Translation": "Edit Translation", "Filter Translations": "Filter Translations", "Add Page": "Add Page", "Edit Page": "Edit Page", "Filter Pages": "Filter Pages", "Published Date Range": "Published Date Range", "To": "To", "Start date": "Start date", "End date": "End date", "Create new": "Create new", "Action": "Action", "Edit": "Edit", "Destroy": "Destroy", "Restore": "Rest<PERSON>", "Force Delete": "Force Delete", "Bulk Delete": "Bulk Delete", "Bulk Destroy": "Bulk Destroy", "Reset": "Reset", "Search": "Search", "Import": "Import", "Export": "Export", "Sync": "Sync", "Publish": "Publish", "Unpublish": "Unpublish", "Duplicate": "Duplicate", "Bulk Publish": "Bulk Publish", "Bulk Unpublish": "Bulk Unpublish", "Import Translations": "Import Translations", "Download Template": "Download Template", "Sync English": "Sync English", "Sync Vietnamese": "Sync Vietnamese", "Sync All Languages": "Sync All Languages", "Please follow these steps to import translations:": "Please follow these steps to import translations:", "Download the template file": "Download the template file", "Fill in your translation data": "Fill in your translation data", "Upload the completed file": "Upload the completed file", "Drop file here or": "Drop file here or", "click to upload": "click to upload", "Only Excel files (.xlsx, .xls) are supported, max size 10MB": "Only Excel files (.xlsx, .xls) are supported, max size 10MB", "Please upload Excel file (.xlsx or .xls)": "Please upload Excel file (.xlsx or .xls)", "File size cannot exceed 10MB": "File size cannot exceed 10MB", "Are you sure you want to delete this language?": "Are you sure you want to delete this language?", "Are you sure you want to delete selected languages?": "Are you sure you want to delete selected languages?", "Are you sure you want to permanently delete this language?": "Are you sure you want to permanently delete this language?", "Are you sure you want to permanently delete selected languages?": "Are you sure you want to permanently delete selected languages?", "Are you sure you want to restore this language?": "Are you sure you want to restore this language?", "Are you sure you want to restore selected languages?": "Are you sure you want to restore selected languages?", "Are you sure you want to delete this translation?": "Are you sure you want to delete this translation?", "Are you sure you want to delete selected translations?": "Are you sure you want to delete selected translations?", "Are you sure you want to permanently delete this translation?": "Are you sure you want to permanently delete this translation?", "Are you sure you want to permanently delete selected translations?": "Are you sure you want to permanently delete selected translations?", "Are you sure you want to restore this translation?": "Are you sure you want to restore this translation?", "Are you sure you want to restore selected translations?": "Are you sure you want to restore selected translations?", "Are you sure you want to delete this page?": "Are you sure you want to delete this page?", "Are you sure you want to delete selected pages?": "Are you sure you want to delete selected pages?", "Are you sure you want to permanently delete this page?": "Are you sure you want to permanently delete this page?", "Are you sure you want to permanently delete selected pages?": "Are you sure you want to permanently delete selected pages?", "Are you sure you want to restore this page?": "Are you sure you want to restore this page?", "Are you sure you want to restore selected pages?": "Are you sure you want to restore selected pages?", "Warning": "Warning", "Confirm": "Confirm", "OK": "OK", "Cancel": "Cancel", "Get failed": "Get failed", "Create successful": "Create successful", "Update successful": "Update successful", "Delete successful": "Delete successful", "Destroy successful": "Destroy successful", "Restore successful": "Restore successful", "Operation failed": "Operation failed", "Delete failed": "Delete failed", "Destroy failed": "Destroy failed", "Restore failed": "Rest<PERSON> failed", "Sync successful": "Sync successful", "Sync failed": "Sync failed", "Export successful": "Export successful", "Export failed": "Export failed", "Import successful": "Import successful", "Import failed": "Import failed", "Publish successful": "Publish successful", "Publish failed": "Publish failed", "Unpublish successful": "Unpublish successful", "Unpublish failed": "Unpublish failed", "Bulk publish successful": "Bulk publish successful", "Bulk publish failed": "Bulk publish failed", "Bulk unpublish successful": "Bulk unpublish successful", "Bulk unpublish failed": "Bulk unpublish failed", "Duplicate successful": "Duplicate successful", "Duplicate failed": "Duplicate failed", "Upload successful": "Upload successful", "Upload failed": "Upload failed", "Please select items to delete": "Please select items to delete", "Please select items to destroy": "Please select items to destroy", "Please select items to restore": "Please select items to restore", "Please select items to publish": "Please select items to publish", "Please select items to unpublish": "Please select items to unpublish", "Bot Management": "Bot Management", "Create Bot": "Create <PERSON><PERSON>", "My Bots": "My Bots", "Bot Store": "Bot Store", "Bot": "Bot", "Hello! How can I help you?": "Hello! How can I help you?", "Please enter AI Assistant Name": "Please enter AI Assistant Name", "Length from 3 to 50 characters": "Length from 3 to 50 characters", "Please select LLM model": "Please select LLM model", "Please enter System Prompt": "Please enter System Prompt", "Prompt needs at least 20 characters to be effective": "Prompt needs at least 20 characters to be effective", "File \"{fileName}\" is already in the list.": "File \"{fileName}\" is already in the list.", "Are you sure you want to clear all current configuration?": "Are you sure you want to clear all current configuration?", "Form has been reset.": "Form has been reset.", "Briefly describe the role of the Agent:": "Briefly describe the role of the Agent:", "✨ Prompt Generator Assistant": "✨ Prompt Generator Assistant", "Generate": "Generate", "Example: Vietnamese literature lesson planning assistant": "Example: Vietnamese literature lesson planning assistant", "Prompt generated successfully!": "Prompt generated successfully!", "Please enter AI Assistant Name first.": "Please enter AI Assistant Name first.", "Please create System Prompt for best suggestions.": "Please create System Prompt for best suggestions.", "AI returned data not in string array format.": "AI returned data not in string array format.", "Cannot parse suggestions from AI.": "Cannot parse suggestions from AI.", "Please fill in all required fields.": "Please fill in all required fields.", "AI Agent Studio": "AI Agent Studio", "New Agent": "New Agent", "Save & Deploy": "Save & Deploy", "Avatar": "Avatar", "Upload JPG, PNG, JPEG images. Size under 5MB.": "Upload JPG, PNG, JPEG images. Size under 5MB.", "Advanced Settings": "Advanced Settings", "Use Knowledge Base": "Use Knowledge Base", "Enable this feature to allow Agent access to your private knowledge sources.": "Enable this feature to allow Agent access to your private knowledge sources.", "Basic Information": "Basic Information", "AI Assistant Name": "AI Assistant Name", "Example: Administrative Procedure Consultant": "Example: Administrative Procedure Consultant", "AI Model": "AI Model", "Vietnamese": "Vietnamese", "English": "English", "Brief description of AI Assistant functions.": "Brief description of AI Assistant functions.", "AI Brain": "AI Brain", "Suggestion - Prompt": "Suggestion - Prompt", "This is the most important part...": "This is the most important part...", "Chat Interface": "Chat Interface", "Welcome Message": "Welcome Message", "Example: Hello! How can I help you?": "Example: Hello! How can I help you?", "✨ Generate": "✨ Generate", "Starter Suggestions": "Starter Suggestions", "Example: What is the tuition fee?": "Example: What is the tuition fee?", "Add Suggestion": "Add Suggestion", "Preview": "Preview", "AI Assistant": "AI Assistant", "Knowledge Base": "Knowledge Base", "Knowledge Base Management": "Knowledge Base Management", "Knowledge Base Name": "Knowledge Base Name", "Owner Type": "Owner Type", "Owner ID": "Owner ID", "Storage Path": "Storage Path", "Knowledge Base Information": "Knowledge Base Information", "Upload New Files": "Upload New Files", "Drag files here or": "Drag files here or", "Text": "Text", "Paste text content here.": "Paste text content here.", "Document Library": "Document Library", "Select previously uploaded documents to add to the knowledge for this Agent.": "Select previously uploaded documents to add to the knowledge for this Agent.", "File Name": "File Name", "Type": "Type", "Upload Date": "Upload Date", "Add to Bot Knowledge": "Add to Bot Knowledge", "Clear Selection": "Clear Selection", "In Bot": "In Bot", "Add Files": "Add Files", "Add Files from Library": "Add Files from Library", "Files currently attached to this bot's knowledge base.": "Files currently attached to this bot's knowledge base.", "Click 'Add Files' to select files from your library.": "Click 'Add Files' to select files from your library.", "Select files from your library to add to this bot's knowledge.": "Select files from your library to add to this bot's knowledge.", "Remove this file from bot knowledge?": "Remove this file from bot knowledge?", "File removed from bot knowledge": "File removed from bot knowledge", "Files added successfully": "Files added successfully", "Add Selected Files": "Add Selected Files", "Already Added": "Already Added", "Selected files are already added to this bot": "Selected files are already added to this bot", "Added {count} files to bot knowledge": "Added {count} files to bot knowledge", "Manage your personal AI Bots": "Manage your personal AI Bots", "Create New Bot": "Create New Bot", "Total Bots": "Total Bots", "Conversations": "Conversations", "Search Bots...": "Search Bots...", "Sort": "Sort", "Recently Updated": "Recently Updated", "Newest Created": "Newest Created", "Name A-Z": "Name A-Z", "No Bots Found": "No Bots Found", "Try changing filters or create a new Bot": "Try changing filters or create a new Bot", "Create First Bot": "Create First Bot", "Unknown": "Unknown", "Paused": "Paused", "Enter name for new Bot:": "Enter name for new Bot:", "Duplicate Bot": "Duplicate <PERSON>", "Please enter Bot name": "Please enter Bot name", "Are you sure you want to delete Bot \"{botName}\"?": "Are you sure you want to delete Bot \"{botName}\"?", "Confirm Delete": "Confirm Delete", "activate": "activate", "pause": "pause", "Are you sure you want to {action} Bot \"{botName}\"?": "Are you sure you want to {action} Bot \"{botName}\"?", "Confirm {action}": "Confirm {action}", "Pause": "Pause", "Activate": "Activate", "Bot Name": "Bot Name", "Please input bot name": "Please input bot name", "Length must be between 1 and 120 characters": "Length must be between 1 and 120 characters", "Enter bot name": "Enter bot name", "Logo": "Logo", "Enter logo URL or upload image": "Enter logo URL or upload image", "Enter logo URL": "Enter logo URL", "or": "or", "Enter bot description": "Enter bot description", "Please select an AI model": "Please select an AI model", "Select AI model": "Select AI model", "System Prompt": "System Prompt", "Please input system prompt": "Please input system prompt", "Enter system prompt that shapes the bot's behavior": "Enter system prompt that shapes the bot's behavior", "Greeting Message": "Greeting Message", "Enter greeting message": "Enter greeting message", "Closing Message": "Closing Message", "Enter closing message": "Enter closing message", "Tool Calling Mode": "Tool Calling Mode", "Please select tool calling mode": "Please select tool calling mode", "Select tool calling mode": "Select tool calling mode", "Auto": "Auto", "None": "None", "Required": "Required", "Please select visibility": "Please select visibility", "Bot Type": "Bot Type", "Please select bot type": "Please select bot type", "Select bot type": "Select bot type", "Personal": "Personal", "Organization": "Organization", "Please select status": "Please select status", "Review": "Review", "Banned": "Banned", "Bot Information Form": "Bot Information Form", "Submit": "Submit", "Search by bot name": "Search by bot name", "Filter by AI model": "Filter by AI model", "Filter by status": "Filter by status", "Filter by visibility": "Filter by visibility", "Filter by bot type": "Filter by bot type", "Filter by tool calling mode": "Filter by tool calling mode", "Filter by trash status": "Filter by trash status", "Filter": "Filter", "Apply Filter": "Apply Filter", "No.": "No.", "Tool Mode": "Tool Mode", "Operation": "Operation", "Please enter the bot name": "Please enter the bot name", "Please enter the system prompt": "Please enter the system prompt", "Failed to fetch bots": "Failed to fetch bots", "Bot created successfully": "<PERSON><PERSON> created successfully", "Failed to create bot": "Failed to create bot", "Bot updated successfully": "Bot updated successfully", "Failed to update bot": "Failed to update bot", "Bot deleted successfully": "<PERSON><PERSON> deleted successfully", "Failed to delete bot": "Failed to delete bot", "Bots deleted successfully": "<PERSON><PERSON> deleted successfully", "Failed to delete bots": "Failed to delete bots", "Are you sure you want to delete this bot?": "Are you sure you want to delete this bot?", "Are you sure you want to delete selected bots?": "Are you sure you want to delete selected bots?", "Are you sure you want to permanently delete this bot?": "Are you sure you want to permanently delete this bot?", "Bot permanently deleted": "Bo<PERSON> permanently deleted", "Failed to permanently delete bot": "Failed to permanently delete bot", "Are you sure you want to permanently delete selected bots?": "Are you sure you want to permanently delete selected bots?", "Bots permanently deleted": "Bots permanently deleted", "Failed to permanently delete bots": "Failed to permanently delete bots", "Bot restored successfully": "<PERSON><PERSON> restored successfully", "Failed to restore bot": "Failed to restore bot", "Bots restored successfully": "<PERSON><PERSON> restored successfully", "Failed to restore bots": "Failed to restore bots", "User Profile": "User Profile", "Manage your profile and settings": "Manage your profile and settings", "Profile": "Profile", "Account": "Account", "Settings": "Settings", "Security": "Security", "Personal Information": "Personal Information", "Full Name": "Full Name", "Enter your full name": "Enter your full name", "Enter your email": "Enter your email", "Phone": "Phone", "Enter your phone number": "Enter your phone number", "Location": "Location", "Enter your location": "Enter your location", "Company": "Company", "Enter your company": "Enter your company", "Job Title": "Job Title", "Enter your job title": "Enter your job title", "Website": "Website", "Enter your website URL": "Enter your website URL", "Bio": "Bio", "Tell us about yourself": "Tell us about yourself", "Update Profile": "Update Profile", "Account Information": "Account Information", "User ID": "User ID", "UUID": "UUID", "Email Verified": "<PERSON><PERSON>", "Verified": "Verified", "Not Verified": "Not Verified", "Last Login": "Last Login", "Member Since": "Member Since", "Roles & Permissions": "Roles & Permissions", "No roles assigned": "No roles assigned", "No permissions assigned": "No permissions assigned", "Preferences": "Preferences", "Language & Region": "Language & Region", "Timezone": "Timezone", "Date Format": "Date Format", "Time Format": "Time Format", "Appearance": "Appearance", "Theme": "Theme", "Light": "Light", "Dark": "Dark", "Notifications": "Notifications", "Email Notifications": "Email Notifications", "Receive notifications via email": "Receive notifications via email", "Push Notifications": "Push Notifications", "Receive push notifications in browser": "Receive push notifications in browser", "Two-Factor Authentication": "Two-Factor Authentication", "Add an extra layer of security to your account": "Add an extra layer of security to your account", "Save Settings": "Save Settings", "Change Password": "Change Password", "Current Password": "Current Password", "Enter current password": "Enter current password", "Enter new password": "Enter new password", "Confirm new password": "Confirm new password", "Update Password": "Update Password", "Avatar must be an image!": "Avatar must be an image!", "Avatar size must be less than 2MB!": "Avatar size must be less than 2MB!", "Avatar updated successfully!": "Avatar updated successfully!", "Failed to update avatar": "Failed to update avatar", "Failed to upload avatar": "Failed to upload avatar", "Profile updated successfully!": "Profile updated successfully!", "Failed to update profile": "Failed to update profile", "Settings updated successfully!": "Settings updated successfully!", "Failed to update settings": "Failed to update settings", "Password updated successfully!": "Password updated successfully!", "Failed to update password": "Failed to update password", "Passwords do not match": "Passwords do not match", "Please fill in all password fields": "Please fill in all password fields", "Loading user profile...": "Loading user profile...", "User not found": "User not found", "Go Back": "Go Back", "View Profile": "View Profile", "Logout": "Logout", "My Profile": "My Profile", "profile.tabs.profile": "Profile", "profile.tabs.account": "Account", "profile.tabs.chat": "Cha<PERSON>", "profile.tabs.voice": "Voice & Video", "profile.tabs.appearance": "Appearance", "profile.tabs.notification": "Notification", "profile.profilePicture": "Profile Picture", "profile.changePicture": "Change Picture", "profile.deletePicture": "Delete Picture", "profile.profileName": "Profile Name", "profile.username": "Username", "profile.statusRecently": "Status Recently", "profile.aboutMe": "About Me", "profile.accountInfo": "Account Information", "profile.changePassword": "Change Password", "profile.comingSoon": "Coming Soon", "Add User": "Add User", "Edit User": "Edit User", "Please enter name": "Please enter name", "Please enter phone number": "Please enter phone number", "Please select role": "Please select role", "Please enter password": "Please enter password", "Please confirm password": "Please confirm password", "Name is required": "Name is required", "Please enter a valid email": "Please enter a valid email", "Role is required": "Role is required", "Password must be at least 6 characters": "Password must be at least 6 characters", "Password confirmation is required": "Password confirmation is required", "User Management": "User Management", "Search by name": "Search by name", "Search by email": "Search by email", "All Status": "All Status", "Suspended": "Suspended", "Send Password Reset": "Send Password Reset", "Send Email Verification": "Send Email Verification", "Unban": "<PERSON><PERSON>", "Status is required": "Status is required", "Has Avatar": "Has <PERSON>", "No Avatar": "No Avatar", "Select avatar status": "Select avatar status", "Model Name": "Model Name", "Model Key": "Model Key", "Provider": "Provider", "Model AI": "Model AI", "Input Types": "Input Types", "Output Types": "Output Types", "Image": "Image", "Audio": "Audio", "Video": "Video", "Document": "Document", "Supported Sources": "Supported Sources", "AI Model Parameters": "AI Model Parameters", "Context Window (Token)": "Context Window (Token)", "Rate Limit RPM": "Rate Limit RPM", "Max Tokens": "<PERSON>", "Timeout Seconds": "Timeout Seconds", "Billing Type": "Billing Type", "Request": "Request", "Token": "Token", "Hybrid": "Hybrid", "Cost Per Request": "Cost Per Request", "Cost Per 1K Tokens": "Cost Per 1K Tokens", "Cost Per 1K Input": "Cost Per 1K Input", "Cost Per 1K Output": "Cost Per 1K Output", "Note": "Note", "Information Form": "Information Form", "Service": "Service", "Please select at least one input type": "Please select at least one input type", "Please select at least one output type": "Please select at least one output type", "e.g., [{\"type\":\"upload\"}, {\"type\":\"oauth\", \"provider\":\"google_drive\"}]": "e.g., [{\"type\":\"upload\"}, {\"type\":\"oauth\", \"provider\":\"google_drive\"}]", "Please input context window": "Please input context window", "Please input rate limit RPM": "Please input rate limit RPM", "Please input max tokens": "Please input max tokens", "Please input timeout seconds": "Please input timeout seconds", "Please select billing type": "Please select billing type", "Please input cost per request": "Please input cost per request", "Please input cost per 1K tokens": "Please input cost per 1K tokens", "Please input cost per 1K input": "Please input cost per 1K input", "Please input cost per 1K output": "Please input cost per 1K output", "Service created successfully": "Service created successfully", "Service creation failed": "Service creation failed", "Service updated successfully": "Service updated successfully", "Service update failed": "Service update failed", "Service save failed": "Service save failed", "Model key": "Model key", "Model name": "Model name", "Model Provider": "Model Provider", "Model Category": "Model Category", "API Endpoint": "API Endpoint", "Streaming": "Streaming", "Vision": "Vision", "Function Calling (Tools)": "Function Calling (Tools)", "Set Default": "<PERSON>", "Please input model key": "Please input model key", "Please input model name": "Please input model name", "Please input API endpoint": "Please input API endpoint", "Length should be between 2 and 100 characters": "Length should be between 2 and 100 characters", "Key can only contain lowercase letters, numbers, hyphens and underscores": "Key can only contain lowercase letters, numbers, hyphens and underscores", "API endpoint must start with /": "API endpoint must start with /", "Failed to fetch modelAi": "Failed to fetch model AI", "Create failed": "Create failed", "Update failed": "Update failed", "Deleted successfully": "Deleted successfully", "Permanently deleted successfully": "Permanently deleted successfully", "Permanent delete failed": "Permanent delete failed", "Restored successfully": "Restored successfully", "Are you sure you want to delete this item?": "Are you sure you want to delete this item?", "Are you sure you want to delete selected items?": "Are you sure you want to delete selected items?", "Are you sure you want to permanently delete this item? This action cannot be undone.": "Are you sure you want to permanently delete this item? This action cannot be undone.", "Are you sure you want to permanently delete selected items? This action cannot be undone.": "Are you sure you want to permanently delete selected items? This action cannot be undone.", "Are you sure you want to restore this item?": "Are you sure you want to restore this item?", "Are you sure you want to restore selected items?": "Are you sure you want to restore selected items?", "Function Calling": "Function Calling", "Profile Information": "Profile Information", "Enter first name": "Enter first name", "Enter last name": "Enter last name", "Enter email": "Enter email", "Enter phone number": "Enter phone number", "Enter address": "Enter address", "Select birthday": "Select birthday", "Select gender": "Select gender", "Male": "Male", "Female": "Female", "Other": "Other", "This field is required": "This field is required", "Name must be at least 2 characters": "Name must be at least 2 characters", "Name must not exceed 50 characters": "Name must not exceed 50 characters", "Name can only contain letters, spaces, hyphens, and apostrophes": "Name can only contain letters, spaces, hyphens, and apostrophes", "Address must not exceed 255 characters": "Address must not exceed 255 characters", "Birthday cannot be in the future": "Birthday cannot be in the future", "Please enter a valid birthday": "Please enter a valid birthday", "Current password is required": "Current password is required", "Avatar must be an image": "Avatar must be an image", "Avatar size must be less than 2MB": "Avatar size must be less than 2MB", "Avatar updated successfully": "Avatar updated successfully", "Profile updated successfully": "Profile updated successfully", "Password updated successfully": "Password updated successfully", "Back to Dashboard": "Back to Dashboard", "Enter message...": "Enter message...", "Are you sure you want to delete?": "Are you sure you want to delete?", "Currently using": "Currently using", "Choose a suggested question below or enter your message to start the conversation.": "Choose a suggested question below or enter your message to start the conversation.", "Welcome to AI Assistant": "Welcome to AI Assistant", "Please select an assistant from the left list to start a new conversation.": "Please select an assistant from the left list to start a new conversation.", "Last message": "Last message", "Sorry, the page you are visiting does not exist": "Sorry, the page you are visiting does not exist", "Sorry, you do not have permission to access this page": "Sorry, you do not have permission to access this page", "Back Home": "Back Home"}