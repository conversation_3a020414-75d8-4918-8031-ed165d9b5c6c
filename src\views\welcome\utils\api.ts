import { http } from "@/utils/http";
import type { Result } from "@/utils/response";

// Chatbot Dashboard API endpoints
export const getChatbotDashboardStats = () => {
  return http.request<Result>("get", "/api/v1/auth/dashboard/chatbot-stats");
};

export const getTokenTrend = (params?: { days?: number }) => {
  return http.request<Result>("get", "/api/v1/auth/dashboard/token-trend", {
    params
  });
};

export const getConversationTrend = (params?: { days?: number }) => {
  return http.request<Result>(
    "get",
    "/api/v1/auth/dashboard/conversation-trend",
    {
      params
    }
  );
};

// Export data functionality
export const exportDashboardData = (params?: {
  format?: "csv" | "excel" | "pdf";
  dateRange?: string;
}) => {
  return http.request<Result>("post", "/api/v1/auth/dashboard/export", {
    data: params
  });
};
