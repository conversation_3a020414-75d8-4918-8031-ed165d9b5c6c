/**
 * Widget Mode Detector
 * Determines whether to use JavaScript Widget or Iframe based on environment
 */

import type { WidgetConfig, WidgetMode } from "../types";

export class WidgetModeDetector {
  private config: WidgetConfig;

  constructor(config: WidgetConfig) {
    this.config = config;
  }

  /**
   * Determine the best widget mode to use
   */
  detectMode(): WidgetMode {
    // Force iframe if explicitly requested
    if (this.config.forceIframe || this.config.mode === "iframe") {
      return "iframe";
    }

    // Force widget if explicitly requested
    if (this.config.mode === "widget") {
      return "widget";
    }

    // Auto-detection logic
    if (this.shouldUseIframe()) {
      return "iframe";
    }

    return "widget";
  }

  /**
   * Check if iframe mode should be used
   */
  private shouldUseIframe(): boolean {
    return (
      this.hasJavaScriptConflicts() ||
      this.hasCSSConflicts() ||
      this.isOldBrowser() ||
      this.hasSecurityRestrictions()
    );
  }

  /**
   * Detect JavaScript conflicts
   */
  private hasJavaScriptConflicts(): boolean {
    // Check for Vue version conflicts
    if (typeof window !== "undefined" && (window as any).Vue) {
      const vue = (window as any).Vue;

      // If Vue 2 is present, use iframe to avoid conflicts
      if (vue.version && vue.version.startsWith("2.")) {
        console.warn("Vue 2 detected, using iframe mode to avoid conflicts");
        return true;
      }
    }

    // Check for other potential conflicts
    const conflictingLibraries = ["React", "Angular", "jQuery"];
    for (const lib of conflictingLibraries) {
      if (typeof window !== "undefined" && (window as any)[lib]) {
        console.info(
          `${lib} detected, considering iframe mode for better isolation`
        );
        // Note: These don't necessarily require iframe, but it's safer
      }
    }

    return false;
  }

  /**
   * Detect CSS conflicts
   */
  private hasCSSConflicts(): boolean {
    if (typeof document === "undefined") return false;

    // Check for CSS frameworks that might conflict
    const cssFrameworks = [
      { name: "Bootstrap", selector: 'link[href*="bootstrap"]' },
      { name: "Foundation", selector: 'link[href*="foundation"]' },
      { name: "Bulma", selector: 'link[href*="bulma"]' },
      { name: "Semantic UI", selector: 'link[href*="semantic"]' }
    ];

    for (const framework of cssFrameworks) {
      if (document.querySelector(framework.selector)) {
        console.info(
          `${framework.name} detected, using iframe for CSS isolation`
        );
        return true;
      }
    }

    // Check for Tailwind CSS
    if (
      document.querySelector('script[src*="tailwind"]') ||
      document.querySelector('link[href*="tailwind"]')
    ) {
      console.info("Tailwind CSS detected, using iframe for CSS isolation");
      return true;
    }

    return false;
  }

  /**
   * Check browser compatibility
   */
  private isOldBrowser(): boolean {
    if (typeof window === "undefined") return false;

    // Check for modern browser features
    const modernFeatures = [
      "fetch",
      "Promise",
      "Map",
      "Set",
      "Symbol",
      "Proxy"
    ];

    for (const feature of modernFeatures) {
      if (!(feature in window)) {
        console.warn(`Missing ${feature}, using iframe mode for compatibility`);
        return true;
      }
    }

    // Check for ES6 support
    try {
      new Function("() => {}");
    } catch (e) {
      console.warn("ES6 not supported, using iframe mode");
      return true;
    }

    return false;
  }

  /**
   * Check for security restrictions
   */
  private hasSecurityRestrictions(): boolean {
    if (typeof window === "undefined") return false;

    // Check Content Security Policy
    const meta = document.querySelector(
      'meta[http-equiv="Content-Security-Policy"]'
    );
    if (meta) {
      const csp = meta.getAttribute("content") || "";
      if (csp.includes("script-src") && !csp.includes("unsafe-inline")) {
        console.warn("Strict CSP detected, using iframe mode");
        return true;
      }
    }

    // Check for iframe restrictions
    try {
      if (window.self !== window.top) {
        // We're already in an iframe, prefer widget mode
        return false;
      }
    } catch (e) {
      // Cross-origin iframe, use iframe mode
      return true;
    }

    return false;
  }

  /**
   * Get detection report for debugging
   */
  getDetectionReport(): {
    recommendedMode: WidgetMode;
    reasons: string[];
    conflicts: {
      javascript: boolean;
      css: boolean;
      browser: boolean;
      security: boolean;
    };
  } {
    const conflicts = {
      javascript: this.hasJavaScriptConflicts(),
      css: this.hasCSSConflicts(),
      browser: this.isOldBrowser(),
      security: this.hasSecurityRestrictions()
    };

    const reasons: string[] = [];

    if (this.config.forceIframe) {
      reasons.push("Force iframe mode requested");
    }

    if (this.config.mode === "iframe") {
      reasons.push("Iframe mode explicitly set");
    }

    if (this.config.mode === "widget") {
      reasons.push("Widget mode explicitly set");
    }

    if (conflicts.javascript) {
      reasons.push("JavaScript conflicts detected");
    }

    if (conflicts.css) {
      reasons.push("CSS conflicts detected");
    }

    if (conflicts.browser) {
      reasons.push("Browser compatibility issues");
    }

    if (conflicts.security) {
      reasons.push("Security restrictions detected");
    }

    return {
      recommendedMode: this.detectMode(),
      reasons,
      conflicts
    };
  }
}

/**
 * Convenience function to detect widget mode
 */
export function detectWidgetMode(config: WidgetConfig): WidgetMode {
  const detector = new WidgetModeDetector(config);
  return detector.detectMode();
}

/**
 * Get detailed detection report
 */
export function getDetectionReport(config: WidgetConfig) {
  const detector = new WidgetModeDetector(config);
  return detector.getDetectionReport();
}
