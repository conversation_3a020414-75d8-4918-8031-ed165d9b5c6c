# ProcMS Widget API Client

A comprehensive API client for the ProcMS Chatbot Widget with advanced features including retry logic, caching, request/response interceptors, and error handling.

## 🚀 Features

- **Retry Logic** - Automatic retry with exponential backoff
- **Caching** - Intelligent caching with TTL support
- **Interceptors** - Request/response interceptors for customization
- **Error Handling** - Comprehensive error handling with custom error types
- **Rate Limiting** - Built-in rate limiting support
- **File Upload** - Support for file uploads with progress tracking
- **TypeScript** - Full TypeScript support with type definitions
- **Testing** - Comprehensive test suite

## 📦 Installation

```typescript
import { BotApiClient, getBotApiClient } from './api/bot-api';
```

## 🔧 Basic Usage

### Creating an API Client

```typescript
// Method 1: Direct instantiation
const apiClient = new BotApiClient({
  apiKey: 'pk_live_your_api_key_here',
  baseUrl: 'https://api.procms.com',
  timeout: 10000,
  retryAttempts: 3,
  enableCache: true
});

// Method 2: Singleton pattern (recommended)
const apiClient = getBotApiClient('pk_live_your_api_key_here');
```

### Configuration Options

```typescript
interface ApiClientConfig {
  apiKey: string;              // Required: Your API key
  baseUrl?: string;            // API base URL (auto-detected)
  timeout?: number;            // Request timeout (default: 10000ms)
  retryAttempts?: number;      // Retry attempts (default: 3)
  retryDelay?: number;         // Base retry delay (default: 1000ms)
  enableCache?: boolean;       // Enable caching (default: true)
  cacheTimeout?: number;       // Cache TTL (default: 300000ms)
}
```

## 🎯 Core API Methods

### Bot Configuration

```typescript
// Get bot configuration (cached)
const botConfig = await apiClient.getBotConfig('bot-uuid-123');

// Force refresh from server
const freshConfig = await apiClient.getBotConfig('bot-uuid-123', true);

// Get public bot info
const publicInfo = await apiClient.getBotPublicInfo('bot-uuid-123');

// Check bot status
const status = await apiClient.checkBotStatus('bot-uuid-123');

// Validate API access
const access = await apiClient.validateAccess('bot-uuid-123');
```

### Conversation Management

```typescript
// Start a new conversation
const conversation = await apiClient.startConversation(
  'bot-uuid-123',
  'user-id-456',
  { source: 'website', page: '/contact' }
);

// Send a message
const response = await apiClient.sendMessage(
  'bot-uuid-123',
  'conversation-id',
  'Hello, I need help',
  {
    messageType: 'text',
    metadata: { priority: 'high' }
  }
);

// Get conversation history
const history = await apiClient.getConversationHistory(
  'bot-uuid-123',
  'conversation-id',
  { limit: 50, includeMetadata: true }
);

// End conversation with feedback
await apiClient.endConversation('bot-uuid-123', 'conversation-id', {
  rating: 5,
  comment: 'Very helpful!',
  resolved: true
});
```

### Advanced Features

```typescript
// Send typing indicator
await apiClient.sendTypingIndicator('bot-uuid-123', 'conversation-id', true);

// Mark message as read
await apiClient.markMessageAsRead('bot-uuid-123', 'conversation-id', 'message-id');

// Upload file with progress tracking
const fileResult = await apiClient.uploadFile(
  'bot-uuid-123',
  'conversation-id',
  file,
  (progress) => console.log(`Upload progress: ${progress}%`)
);

// Submit feedback
await apiClient.submitFeedback('bot-uuid-123', 'conversation-id', {
  rating: 4,
  comment: 'Good service',
  helpful: true
});

// Get analytics
const analytics = await apiClient.getWidgetAnalytics('bot-uuid-123', '24h');
```

## 🔧 Advanced Configuration

### Request/Response Interceptors

```typescript
// Add request interceptor
apiClient.addRequestInterceptor((config) => {
  // Add custom headers
  return {
    ...config,
    headers: {
      ...config.headers,
      'X-Custom-Header': 'value'
    }
  };
});

// Add response interceptor
apiClient.addResponseInterceptor(async (response) => {
  // Log all responses
  console.log('API Response:', response.status);
  return response;
});
```

### Caching Control

```typescript
// Clear specific cache entries
apiClient.clearCache('bot-uuid-123');

// Clear all cache
apiClient.clearCache();

// Get cache statistics
const stats = apiClient.getCacheStats();
console.log(`Cache size: ${stats.size}, Keys: ${stats.keys}`);

// Disable caching for specific requests
const result = await apiClient.request('/api/endpoint', {}, false);
```

### Error Handling

```typescript
import { ApiError } from './api/bot-api';

try {
  const result = await apiClient.getBotConfig('invalid-uuid');
} catch (error) {
  if (error instanceof ApiError) {
    console.error(`API Error ${error.status}: ${error.message}`);
    console.error('Error data:', error.data);
  } else {
    console.error('Unexpected error:', error);
  }
}
```

## 🛠️ Utility Functions

### Convenience Functions

```typescript
import { 
  getBotConfiguration,
  validateBotAccess,
  startBotConversation,
  sendBotMessage
} from './api/bot-api';

// Quick bot configuration
const config = await getBotConfiguration('bot-uuid', 'api-key');

// Quick conversation start
const conv = await startBotConversation('bot-uuid', 'api-key', 'user-id');

// Quick message send
const response = await sendBotMessage('bot-uuid', 'api-key', 'conv-id', 'Hello');
```

### API Utilities

```typescript
import { 
  RateLimiter, 
  RequestQueue, 
  DataTransform, 
  UrlUtils,
  StorageUtils 
} from './api/api-utils';

// Rate limiting
const limiter = new RateLimiter(100, 60000); // 100 requests per minute
if (limiter.canMakeRequest()) {
  limiter.recordRequest();
  // Make API call
}

// Request queue
const queue = new RequestQueue(5); // Max 5 concurrent requests
const result = await queue.add(() => apiClient.getBotConfig('bot-uuid'));

// Data transformation
const camelCaseData = DataTransform.toCamelCase(snakeCaseData);
const sanitizedInput = DataTransform.sanitizeInput(userInput);

// URL utilities
const url = UrlUtils.buildUrl('https://api.com', '/bots', { limit: 10 });
const domain = UrlUtils.extractDomain('https://example.com/path');

// Storage utilities
StorageUtils.setItem('cache-key', data, 3600000); // 1 hour TTL
const cachedData = StorageUtils.getItem('cache-key');
```

## 🧪 Testing

```typescript
import { describe, it, expect, vi } from 'vitest';
import { BotApiClient } from './bot-api';

describe('BotApiClient', () => {
  it('should handle successful requests', async () => {
    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({ success: true, data: {} })
    };
    global.fetch = vi.fn().mockResolvedValue(mockResponse);

    const client = new BotApiClient({ apiKey: 'test-key' });
    const result = await client.getBotConfig('bot-uuid');
    
    expect(result.success).toBe(true);
  });
});
```

## 📊 Performance Monitoring

```typescript
import { DebugUtils } from './api/api-utils';

// Performance measurement
const result = await DebugUtils.measurePerformance(
  'Bot Config Load',
  () => apiClient.getBotConfig('bot-uuid')
);

// API call logging
DebugUtils.logApiCall('GET', '/api/bots/config', null, result);
```

## 🔒 Security Best Practices

1. **API Key Protection**
   ```typescript
   // Never log full API keys
   const maskedKey = apiKey.replace(/(.{8}).*(.{4})/, '$1****$2');
   ```

2. **Input Sanitization**
   ```typescript
   const sanitizedMessage = DataTransform.sanitizeInput(userMessage);
   ```

3. **HTTPS Only**
   ```typescript
   // Client automatically enforces HTTPS for production
   ```

4. **Rate Limiting**
   ```typescript
   // Built-in rate limiting prevents abuse
   ```

## 🚨 Error Types

- **ApiError** - HTTP errors with status codes
- **NetworkError** - Connection failures
- **TimeoutError** - Request timeouts
- **ValidationError** - Invalid input data

## 📈 Monitoring & Analytics

The API client automatically tracks:
- Request/response times
- Error rates
- Cache hit rates
- Retry attempts
- Rate limit usage

## 🔄 Migration Guide

### From v1.0 to v2.0

```typescript
// Old way
const client = new BotApiClient(apiKey, baseUrl);

// New way
const client = new BotApiClient({
  apiKey,
  baseUrl,
  timeout: 10000,
  retryAttempts: 3
});
```

## 📝 License

MIT License - see LICENSE file for details.
