const Layout = () => import("@/layout/index.vue");

export default {
  path: "/auth/ai-models/management",
  name: "ModelAI",
  component: Layout,
  redirect: "/auth/ai-models",
  meta: {
    icon: "ri:brain-line",
    title: "AI Models",
    rank: 3,
    // @ts-ignore
    roles: ["super-admin", "admin"]
  },
  children: [
    {
      path: "/auth/ai-models",
      name: "ModelAIIndex",
      component: () => import("@/views/model-ai/ai/index.vue"),
      meta: {
        icon: "ri:cpu-line",
        title: "AI Models",
        showLink: true
      }
    },
    {
      path: "/auth/ai-models/categories",
      name: "ModelCategoryIndex",
      component: () => import("@/views/model-ai/category/index.vue"),
      meta: {
        icon: "ri:folder-3-line",
        title: "AI Categories",
        showLink: true
      }
    },
    {
      path: "/auth/ai-models/provider",
      name: "ModelProviderIndex",
      component: () => import("@/views/model-ai/provider/index.vue"),
      meta: {
        icon: "ri:cloud-line",
        title: "AI Providers",
        showLink: true
      }
    },
  ]
} satisfies RouteConfigsTable;
