<script setup lang="ts">
import { computed } from "vue";
import { ElTag } from "element-plus";

interface Props {
  status: string;
  type?: "default" | "bot" | "chat" | "message" | "user";
  size?: "large" | "default" | "small";
  effect?: "dark" | "light" | "plain";
  round?: boolean;
  closable?: boolean;
}

interface Emits {
  (e: "close"): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: "default",
  size: "default",
  effect: "light",
  round: false,
  closable: false
});

const emit = defineEmits<Emits>();

// Status color mappings for different types
const statusColorMaps = {
  default: {
    active: "success",
    inactive: "info",
    pending: "warning",
    disabled: "danger",
    draft: "info",
    published: "success",
    archived: "warning",
    deleted: "danger"
  },
  bot: {
    draft: "info",
    review: "warning",
    active: "success",
    paused: "warning",
    banned: "danger"
  },
  chat: {
    active: "success",
    archived: "warning",
    deleted: "danger"
  },
  message: {
    sent: "success",
    delivered: "primary",
    read: "success",
    failed: "danger",
    pending: "warning"
  },
  user: {
    active: "success",
    inactive: "info",
    suspended: "warning",
    banned: "danger",
    pending: "warning",
    verified: "success",
    unverified: "warning"
  }
};

// Status icon mappings
const statusIconMaps = {
  default: {
    active: "✓",
    inactive: "○",
    pending: "⏳",
    disabled: "✗",
    draft: "📝",
    published: "✓",
    archived: "📦",
    deleted: "🗑️"
  },
  bot: {
    draft: "📝",
    review: "👁️",
    active: "🤖",
    paused: "⏸️",
    banned: "🚫"
  },
  chat: {
    active: "💬",
    archived: "📦",
    deleted: "🗑️"
  },
  message: {
    sent: "📤",
    delivered: "📨",
    read: "👁️",
    failed: "❌",
    pending: "⏳"
  },
  user: {
    active: "👤",
    inactive: "😴",
    suspended: "⏸️",
    banned: "🚫",
    pending: "⏳",
    verified: "✅",
    unverified: "❓"
  }
};

const tagType = computed(() => {
  const colorMap = statusColorMaps[props.type] || statusColorMaps.default;
  return colorMap[props.status.toLowerCase()] || "info";
});

const statusIcon = computed(() => {
  const iconMap = statusIconMaps[props.type] || statusIconMaps.default;
  return iconMap[props.status.toLowerCase()] || "";
});

const displayText = computed(() => {
  return props.status.charAt(0).toUpperCase() + props.status.slice(1).toLowerCase();
});

const handleClose = () => {
  emit("close");
};
</script>

<template>
  <el-tag
    :type="tagType"
    :size="size"
    :effect="effect"
    :round="round"
    :closable="closable"
    @close="handleClose"
    class="status-badge"
  >
    <span v-if="statusIcon" class="mr-1">{{ statusIcon }}</span>
    {{ displayText }}
  </el-tag>
</template>

<style scoped>
.status-badge {
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
