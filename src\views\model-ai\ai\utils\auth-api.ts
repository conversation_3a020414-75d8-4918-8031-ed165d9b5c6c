import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getModelAi = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/model-ai", { params });
};

export const getModelAiById = (id: number) => {
  return http.request<Result>("get", `/api/v1/auth/model-ai/${id}`);
};

export const getModelAiDropdown = () => {
  return http.request<Result>("get", "/api/v1/auth/model-ai/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createModelAi = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/model-ai", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateModelAiById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/model-ai/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteModelAi = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/model-ai/${id}/delete`);
};

export const bulkDeleteModelAi = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/model-ai/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteModelAiPermanent = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/model-ai/${id}/force`);
};

export const bulkDeleteModelAiPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/model-ai/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreModelAi = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/model-ai/${id}/restore`);
};

export const bulkRestoreModelAi = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/v1/auth/model-ai/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Model Service Operations
 ***************************
 */
export const updateOrCreateModelService = (data: any) => {
  console.log("updateOrCreateModelService called with data:", data);

  // Handle parameters specially to avoid deep conversion issues
  const { allowedParameters, defaultParameters, ...otherData } = data;

  const convertedData = {
    // @ts-ignore
    ...useConvertKeyToSnake(otherData),
    allowed_parameters: allowedParameters,
    default_parameters: defaultParameters
  };

  console.log("Final data to send:", convertedData);

  return http.request<r>("post", "/api/v1/auth/model-ai/service", {
    data: convertedData
  });
};

export const updateModelService = (id: number, data: any) => {
  return http.request<r>("put", `/api/v1/auth/model-service/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const getModelService = (id: number) => {
  return http.request<r>("get", `/api/v1/auth/model-service/${id}`);
};
