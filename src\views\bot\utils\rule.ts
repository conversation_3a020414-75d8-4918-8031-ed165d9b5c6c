import { $t } from "@/plugins/i18n";

export const botRules = {
  name: [
    {
      required: true,
      message: $t("Please input bot name"),
      trigger: ["blur", "change"]
    },
    {
      min: 2,
      max: 100,
      message: $t("Length should be between 2 and 100 characters"),
      trigger: ["blur", "change"]
    }
  ],
  description: [
    {
      max: 500,
      message: $t("Description cannot exceed 500 characters"),
      trigger: ["blur", "change"]
    }
  ],
  model: [
    {
      required: true,
      message: $t("Please select AI model"),
      trigger: ["blur", "change"]
    }
  ],
  systemPrompt: [
    {
      required: true,
      message: $t("Please input system prompt"),
      trigger: ["blur", "change"]
    },
    {
      max: 8000,
      message: $t("System prompt cannot exceed 8000 characters"),
      trigger: ["blur", "change"]
    }
  ],
  greetingMessage: [
    {
      max: 500,
      message: $t("Greeting message cannot exceed 500 characters"),
      trigger: ["blur", "change"]
    }
  ],
  closingMessage: [
    {
      max: 500,
      message: $t("Closing message cannot exceed 500 characters"),
      trigger: ["blur", "change"]
    }
  ],
  status: [
    {
      required: true,
      message: $t("Please select status"),
      trigger: ["blur", "change"]
    },
    {
      required: true,
      message: $t("Please select status"),
      trigger: ["blur", "change"]
    }
  ],
  visibility: [
    {
      required: true,
      message: $t("Please select visibility"),
      trigger: ["blur", "change"]
    }
  ],
  botType: [
    {
      required: true,
      message: $t("Please select bot type"),
      trigger: ["blur", "change"]
    }
  ],
  toolCallingMode: [
    {
      required: true,
      message: $t("Please select tool calling mode"),
      trigger: ["blur", "change"]
    }
  ]
};
