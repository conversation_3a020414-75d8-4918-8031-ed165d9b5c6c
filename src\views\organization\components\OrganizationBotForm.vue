<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { reactive, ref, onMounted } from "vue";
import { useLanguageStoreHook } from "@/store/modules/language";
import {
  Plus,
  Refresh,
  Promotion,
  Delete,
  UploadFilled
} from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import { useRoute } from "vue-router";

// --- State Management ---
const route = useRoute();
const aiModels = ref([]);
const knowledgeTab = ref("files");
const formRef = ref<FormInstance>();

const loading = reactive({
  prompt: false,
  greeting: false,
  starters: false,
  submit: false
});

// Form data
const form = reactive({
  name: "",
  description: "",
  aiModel: "",
  prompt: "",
  greeting: "",
  starters: []
});

// Form validation rules
const rules = reactive<FormRules>({
  name: [
    { required: true, message: $t("Please enter bot name"), trigger: "blur" }
  ],
  description: [
    { required: true, message: $t("Please enter description"), trigger: "blur" }
  ],
  aiModel: [
    { required: true, message: $t("Please select AI model"), trigger: "change" }
  ]
});

// Methods
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.submit = true;

    // Submit logic here
    ElMessage.success($t("Organization bot created successfully"));
  } catch (error) {
    console.error("Form validation failed:", error);
  } finally {
    loading.submit = false;
  }
};

const generatePrompt = () => {
  loading.prompt = true;
  setTimeout(() => {
    form.prompt = $t(
      "You are an AI assistant for organization management. Help users with administrative tasks, scheduling, and information retrieval."
    );
    loading.prompt = false;
  }, 1000);
};

const generateGreeting = () => {
  loading.greeting = true;
  setTimeout(() => {
    form.greeting = $t(
      "Hello! I'm your organization assistant. How can I help you today?"
    );
    loading.greeting = false;
  }, 1000);
};

const generateStarters = () => {
  loading.starters = true;
  setTimeout(() => {
    form.starters = [
      $t("Show me today's schedule"),
      $t("Help me find a document"),
      $t("Create a meeting summary"),
      $t("Check team availability")
    ];
    loading.starters = false;
  }, 1000);
};

onMounted(() => {
  // Load AI models
  aiModels.value = [
    { label: "GPT-4", value: "gpt-4" },
    { label: "GPT-3.5 Turbo", value: "gpt-3.5-turbo" },
    { label: "Claude-3", value: "claude-3" }
  ];
});
</script>

<template>
  <div class="organization-bot-form">
    <div class="p-6">
      <h1 class="text-2xl font-bold text-gray-800 mb-6">
        {{ $t("Create Organization Bot") }}
      </h1>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="max-w-4xl"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('Bot Name')" prop="name">
              <el-input
                v-model="form.name"
                :placeholder="$t('Enter bot name')"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('AI Model')" prop="aiModel">
              <el-select
                v-model="form.aiModel"
                :placeholder="$t('Select AI model')"
                class="w-full"
              >
                <el-option
                  v-for="model in aiModels"
                  :key="model.value"
                  :label="model.label"
                  :value="model.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item :label="$t('Description')" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            :placeholder="$t('Enter bot description')"
          />
        </el-form-item>

        <el-form-item :label="$t('System Prompt')">
          <div class="w-full">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-600">{{
                $t("Define bot behavior")
              }}</span>
              <el-button
                size="small"
                :loading="loading.prompt"
                @click="generatePrompt"
              >
                <template #icon>
                  <Refresh />
                </template>
                {{ $t("Generate") }}
              </el-button>
            </div>
            <el-input
              v-model="form.prompt"
              type="textarea"
              :rows="4"
              :placeholder="$t('Enter system prompt')"
            />
          </div>
        </el-form-item>

        <el-form-item :label="$t('Greeting Message')">
          <div class="w-full">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-600">{{
                $t("First message to users")
              }}</span>
              <el-button
                size="small"
                :loading="loading.greeting"
                @click="generateGreeting"
              >
                <template #icon>
                  <Refresh />
                </template>
                {{ $t("Generate") }}
              </el-button>
            </div>
            <el-input
              v-model="form.greeting"
              type="textarea"
              :rows="2"
              :placeholder="$t('Enter greeting message')"
            />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading.submit"
            @click="handleSubmit"
          >
            {{ $t("Create Bot") }}
          </el-button>
          <el-button @click="$router.back()">
            {{ $t("Cancel") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.organization-bot-form {
  @apply min-h-screen bg-gray-50;
}
</style>
