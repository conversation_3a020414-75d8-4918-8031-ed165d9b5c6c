<script setup lang="ts">
import { ref, computed } from "vue";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage } from "element-plus";
import type { UploadProps, UploadUserFile, UploadFile } from "element-plus";

interface Props {
  modelValue?: string | string[];
  multiple?: boolean;
  accept?: string;
  maxSize?: number; // in MB
  maxCount?: number;
  disabled?: boolean;
  listType?: "text" | "picture" | "picture-card";
  autoUpload?: boolean;
  showFileList?: boolean;
  drag?: boolean;
  action?: string;
  headers?: Record<string, string>;
  data?: Record<string, any>;
  name?: string;
  withCredentials?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: string | string[]): void;
  (e: "change", files: UploadFile[]): void;
  (e: "success", response: any, file: UploadFile): void;
  (e: "error", error: Error, file: UploadFile): void;
  (e: "progress", event: ProgressEvent, file: UploadFile): void;
  (e: "remove", file: UploadFile): void;
  (e: "preview", file: UploadFile): void;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  accept: "*",
  maxSize: 10, // 10MB
  maxCount: 1,
  disabled: false,
  listType: "text",
  autoUpload: true,
  showFileList: true,
  drag: false,
  action: "/api/upload",
  name: "file",
  withCredentials: true
});

const emit = defineEmits<Emits>();

const uploadRef = ref();
const fileList = ref<UploadUserFile[]>([]);

const acceptText = computed(() => {
  if (props.accept === "*") return $t("All files");
  if (props.accept.includes("image")) return $t("Images");
  if (props.accept.includes("video")) return $t("Videos");
  if (props.accept.includes("audio")) return $t("Audio files");
  if (props.accept.includes("pdf")) return $t("PDF files");
  return props.accept;
});

const maxSizeText = computed(() => {
  return `${props.maxSize}MB`;
});

const uploadTip = computed(() => {
  const tips = [];
  tips.push($t("Supported formats: {formats}", { formats: acceptText.value }));
  tips.push($t("Maximum size: {size}", { size: maxSizeText.value }));
  if (props.multiple && props.maxCount > 1) {
    tips.push($t("Maximum {count} files", { count: props.maxCount }));
  }
  return tips.join(", ");
});

// File validation
const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  // Check file size
  const isValidSize = file.size / 1024 / 1024 < props.maxSize;
  if (!isValidSize) {
    ElMessage.error($t("File size cannot exceed {size}!", { size: maxSizeText.value }));
    return false;
  }

  // Check file type if specified
  if (props.accept !== "*") {
    const isValidType = props.accept.split(",").some(type => {
      if (type.includes("*")) {
        const mainType = type.split("/")[0];
        return file.type.startsWith(mainType);
      }
      return file.type === type.trim();
    });
    
    if (!isValidType) {
      ElMessage.error($t("Invalid file type! Supported: {types}", { types: acceptText.value }));
      return false;
    }
  }

  // Check file count
  if (props.multiple && fileList.value.length >= props.maxCount) {
    ElMessage.error($t("Cannot upload more than {count} files", { count: props.maxCount }));
    return false;
  }

  return true;
};

// Upload handlers
const handleSuccess = (response: any, file: UploadFile) => {
  emit("success", response, file);
  updateModelValue();
};

const handleError = (error: Error, file: UploadFile) => {
  ElMessage.error($t("Upload failed: {error}", { error: error.message }));
  emit("error", error, file);
};

const handleProgress = (event: ProgressEvent, file: UploadFile) => {
  emit("progress", event, file);
};

const handleRemove = (file: UploadFile) => {
  emit("remove", file);
  updateModelValue();
};

const handlePreview = (file: UploadFile) => {
  emit("preview", file);
};

const handleChange = (file: UploadFile, files: UploadFile[]) => {
  fileList.value = files;
  emit("change", files);
  updateModelValue();
};

// Update model value based on uploaded files
const updateModelValue = () => {
  const urls = fileList.value
    .filter(file => file.status === "success")
    .map(file => file.response?.url || file.url)
    .filter(Boolean);

  if (props.multiple) {
    emit("update:modelValue", urls);
  } else {
    emit("update:modelValue", urls[0] || "");
  }
};

// Public methods
const submit = () => {
  uploadRef.value?.submit();
};

const clearFiles = () => {
  uploadRef.value?.clearFiles();
  fileList.value = [];
  emit("update:modelValue", props.multiple ? [] : "");
};

const abort = (file?: UploadFile) => {
  uploadRef.value?.abort(file);
};

defineExpose({
  submit,
  clearFiles,
  abort
});
</script>

<template>
  <div class="file-upload-wrapper">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="action"
      :headers="headers"
      :data="data"
      :name="name"
      :with-credentials="withCredentials"
      :multiple="multiple"
      :accept="accept"
      :disabled="disabled"
      :list-type="listType"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      :drag="drag"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-change="handleChange"
      class="file-upload"
    >
      <template v-if="drag">
        <div class="upload-dragger">
          <el-icon class="upload-icon">
            <IconifyIconOffline :icon="useRenderIcon('ri/upload-cloud-2-line')" />
          </el-icon>
          <div class="upload-text">
            {{ $t("Drop file here or") }}
            <em>{{ $t("click to upload") }}</em>
          </div>
        </div>
      </template>
      
      <template v-else-if="listType === 'picture-card'">
        <el-icon class="upload-icon">
          <IconifyIconOffline :icon="useRenderIcon('ri/add-line')" />
        </el-icon>
      </template>
      
      <template v-else>
        <el-button type="primary" :disabled="disabled">
          <el-icon class="mr-2">
            <IconifyIconOffline :icon="useRenderIcon('ri/upload-line')" />
          </el-icon>
          {{ $t("Click to upload") }}
        </el-button>
      </template>
      
      <template #tip>
        <div class="upload-tip">
          {{ uploadTip }}
        </div>
      </template>
    </el-upload>
  </div>
</template>

<style scoped>
.file-upload-wrapper {
  width: 100%;
}

.upload-dragger {
  padding: 40px;
  text-align: center;
  border: 2px dashed var(--el-border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.upload-dragger:hover {
  border-color: var(--el-color-primary);
}

.upload-icon {
  font-size: 48px;
  color: var(--el-text-color-placeholder);
  margin-bottom: 16px;
}

.upload-text {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.upload-text em {
  color: var(--el-color-primary);
  font-style: normal;
}

.upload-tip {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.4;
}

:deep(.el-upload-list) {
  margin-top: 8px;
}

:deep(.el-upload-list__item) {
  transition: all 0.3s ease;
}

:deep(.el-upload-list__item:hover) {
  background-color: var(--el-fill-color-light);
}
</style>
