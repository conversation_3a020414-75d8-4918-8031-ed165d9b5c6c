<script setup lang="ts">
import { ref, watch } from "vue";
import { Plus, Loading } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { getToken } from "@/utils/auth";
import type { UploadProps, UploadFile } from "element-plus";

interface Props {
  logoUrl?: string;
  logo?: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "upload-success", file: any): void;
  (e: "upload-error", error: any): void;
}>();

const uploading = ref(false);
const avatarFiles = ref<UploadFile[]>([]);

const beforeAvatarUpload: UploadProps["beforeUpload"] = rawFile => {
  const isValidType = ["image/jpeg", "image/jpg", "image/png"].includes(
    rawFile.type
  );
  const isLt5M = rawFile.size / 1024 / 1024 < 5;

  if (!isValidType) {
    message($t("Avatar must be JPG, PNG or JPEG format!"), { type: "error" });
    return false;
  }
  if (!isLt5M) {
    message($t("Avatar size must be less than 5MB!"), { type: "error" });
    return false;
  }

  // Emit change event with file preview
  emit("change", {
    raw: rawFile,
    logoUrl: URL.createObjectURL(rawFile)
  });

  uploading.value = true;
  return true;
};

const handleUploadSuccess = (response: any, file: UploadFile) => {
  uploading.value = false;
  if (response.success && response.data) {
    message($t("Avatar uploaded successfully!"), { type: "success" });
    console.log("--------------------->:::", response.data);
    emit("upload-success", response.data);
    // Clear the file list after successful upload
    avatarFiles.value = [];
  } else {
    message(response.message || $t("Avatar upload failed!"), { type: "error" });
    emit("upload-error", response);
  }
};

const handleUploadError = (error: any) => {
  uploading.value = false;
  console.error("Avatar upload error:", error);
  message($t("Avatar upload failed!"), { type: "error" });
  emit("upload-error", error);
  // Clear the file list on error
  avatarFiles.value = [];
};
</script>

<template>
  <div class="card">
    <h2 class="section-title !block text-center">{{ $t("Avatar") }}</h2>
    <div class="flex justify-center">
      <el-upload
        v-model:file-list="avatarFiles"
        class="avatar-uploader"
        action="/api/v1/auth/bots/upload-avatar"
        :show-file-list="false"
        :auto-upload="true"
        :headers="{
          Authorization: `Bearer ${getToken().accessToken ?? getToken()}`,
          'X-Requested-With': 'XMLHttpRequest'
        }"
        :before-upload="beforeAvatarUpload"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      >
        <div v-if="uploading" class="avatar-uploader-loading">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <div class="loading-text">{{ $t("Uploading...") }}</div>
        </div>
        <img
          v-else-if="props.logoUrl"
          :src="props.logoUrl"
          class="avatar"
          alt="avatar"
        />
        <el-icon v-else class="avatar-uploader-icon">
          <Plus />
        </el-icon>
      </el-upload>
    </div>
    <p class="text-xs text-center text-gray-500 !mt-4">
      {{ $t("Upload JPG, PNG, JPEG images. Size under 5MB.") }}
    </p>
  </div>
</template>

<style lang="scss" scoped>
.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
    object-fit: cover;
  }

  .avatar-uploader-loading {
    width: 178px;
    height: 178px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--el-fill-color-light);
    border-radius: 6px;

    .el-icon {
      font-size: 28px;
      color: var(--el-color-primary);
      margin-bottom: 8px;
    }

    .loading-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
}
</style>
