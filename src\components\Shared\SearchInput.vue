<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDebounceFn } from "@vueuse/core";

interface Props {
  modelValue?: string;
  placeholder?: string;
  clearable?: boolean;
  disabled?: boolean;
  size?: "large" | "default" | "small";
  prefixIcon?: string;
  suffixIcon?: string;
  debounce?: number;
  loading?: boolean;
  maxlength?: number;
  showWordLimit?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: string): void;
  (e: "search", value: string): void;
  (e: "clear"): void;
  (e: "focus", event: FocusEvent): void;
  (e: "blur", event: FocusEvent): void;
  (e: "input", value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "",
  clearable: true,
  disabled: false,
  size: "default",
  prefixIcon: "ri/search-line",
  debounce: 300,
  loading: false,
  maxlength: 255,
  showWordLimit: false
});

const emit = defineEmits<Emits>();

const inputRef = ref();
const internalValue = ref(props.modelValue);

const placeholderText = computed(() => {
  return props.placeholder || $t("Search...");
});

// Debounced search function
const debouncedSearch = useDebounceFn((value: string) => {
  emit("search", value);
}, props.debounce);

// Watch for external value changes
watch(() => props.modelValue, (newValue) => {
  internalValue.value = newValue;
});

// Watch for internal value changes
watch(internalValue, (newValue) => {
  emit("update:modelValue", newValue);
  emit("input", newValue);
  debouncedSearch(newValue);
});

const handleClear = () => {
  internalValue.value = "";
  emit("clear");
  emit("search", "");
};

const handleFocus = (event: FocusEvent) => {
  emit("focus", event);
};

const handleBlur = (event: FocusEvent) => {
  emit("blur", event);
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter") {
    event.preventDefault();
    emit("search", internalValue.value);
  }
};

// Public methods
const focus = () => {
  inputRef.value?.focus();
};

const blur = () => {
  inputRef.value?.blur();
};

const select = () => {
  inputRef.value?.select();
};

defineExpose({
  focus,
  blur,
  select
});
</script>

<template>
  <div class="search-input-wrapper">
    <el-input
      ref="inputRef"
      v-model="internalValue"
      :placeholder="placeholderText"
      :clearable="clearable"
      :disabled="disabled"
      :size="size"
      :maxlength="maxlength"
      :show-word-limit="showWordLimit"
      class="search-input"
      @clear="handleClear"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
    >
      <template #prefix>
        <el-icon v-if="loading" class="is-loading">
          <IconifyIconOffline :icon="useRenderIcon('ri/loader-4-line')" />
        </el-icon>
        <el-icon v-else-if="prefixIcon">
          <IconifyIconOffline :icon="useRenderIcon(prefixIcon)" />
        </el-icon>
      </template>
      
      <template #suffix>
        <el-icon v-if="suffixIcon">
          <IconifyIconOffline :icon="useRenderIcon(suffixIcon)" />
        </el-icon>
      </template>
    </el-input>
  </div>
</template>

<style scoped>
.search-input-wrapper {
  width: 100%;
}

.search-input {
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper) {
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-icon.is-loading) {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

:deep(.el-input__prefix) {
  color: var(--el-text-color-placeholder);
}

:deep(.el-input__suffix) {
  color: var(--el-text-color-placeholder);
}
</style>
