import { getPluginsList } from "./build/plugins";
import { include, exclude } from "./build/optimize";
import { type UserConfigExport, type ConfigEnv, loadEnv } from "vite";
import {
  root,
  alias,
  wrapperEnv,
  pathResolve,
  __APP_INFO__
} from "./build/utils";

export default ({ mode }: ConfigEnv): UserConfigExport => {
  // Added VITE_API_BASE_URL to the destructuring assignment to make it available
  const {
    VITE_CDN,
    VITE_PORT,
    VITE_COMPRESSION,
    VITE_PUBLIC_PATH,
    VITE_API_BASE_URL // This variable is now correctly loaded
  } = wrapperEnv(loadEnv(mode, root));
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias
    },
    // Server options
    server: {
      // Port number
      port: VITE_PORT,
      host: "0.0.0.0",
      // Local cross-domain proxy: https://vitejs.dev/config/server-options.html#server-proxy
      proxy: {
        // Main configuration: Handle all API calls
        "/api": {
          target: VITE_API_BASE_URL,
          changeOrigin: true
        }
      },
      // Warm up files to pre-transform and cache results, reducing initial page load time during startup and preventing transform waterfalls.
      warmup: {
        clientFiles: ["./index.html", "./src/{views,components}/*"]
      }
    },
    plugins: getPluginsList(VITE_CDN, VITE_COMPRESSION),
    // Dependency optimization options: https://vitejs.dev/config/dep-optimization-options.html
    optimizeDeps: {
      include,
      exclude
    },
    build: {
      // Browser compatibility target: https://vitejs.dev/guide/build.html#browser-compatibility
      target: "es2015",
      sourcemap: false,
      // Eliminate the warning for bundle sizes exceeding 500kb
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: pathResolve("./index.html", import.meta.url)
        },
        // Classify and package static resources
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    }
  };
};
