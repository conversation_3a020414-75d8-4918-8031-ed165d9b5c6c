<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Username")),
    prop: "username",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("First name")),
    prop: "firstName",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Last name")),
    prop: "lastName",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Email")),
    prop: "email",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Phone")),
    prop: "phone",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Suspended"), value: "suspended" },
      { label: $t("Banned"), value: "banned" },
      { label: $t("Pending"), value: "pending" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("No"), value: "no" },
      { label: $t("Yes"), value: "yes" }
    ]
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
};

const handleReset = () => {
  emit("reset");
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button round @click="handleReset">
          <IconifyIconOnline icon="tabler:refresh" class="mr-1.5" />
          {{ $t("Reset") }}
        </el-button>
        <el-button
          round
          type="warning"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          <IconifyIconOnline icon="tabler:filter" class="mr-1.5" />
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
