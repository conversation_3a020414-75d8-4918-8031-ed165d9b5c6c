import { defineStore } from "pinia";
import { store } from "@/store";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { getBots } from "@/views/bot/utils/api";
import { storageLocal } from "@pureadmin/utils";

export interface Bot {
  id: number;
  name: string;
}

interface BotState {
  bots: Bot[] | null;
}

export const useBotStore = defineStore("proCMS-bot", {
  state: (): BotState => {
    const bots = storageLocal().getItem<Bot[]>("bots");
    return {
      bots
    };
  },

  getters: {
    getBots: state => state.bots
  },

  actions: {
    setBots(data: any) {
      this.bots = data;
    },
    async fetchPublicBots() {
      try {
        const { data } = await getBots();
        this.setBots(useConvertKeyToCamel(data));
      } catch (error) {
        console.error("Failed to fetch bots:", error);
      }
    }
  }
});

export function useBotStoreHook() {
  return useBotStore(store);
}
