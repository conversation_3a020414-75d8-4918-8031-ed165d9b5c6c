const Layout = () => import("@/layout/index.vue");

export default {
  path: "/auth/organizations/management",
  name: "Organization",
  component: Layout,
  redirect: "/auth/organizations",
  meta: {
    icon: "ri:building-line",
    title: "Organizations",
    rank: 9,
    // @ts-ignore
    roles: ["super-admin", "admin"]
  },
  children: [
    {
      path: "/auth/organizations",
      name: "OrganizationIndex",
      component: () => import("@/views/organization/index.vue"),
      meta: {
        icon: "ri:building-line",
        title: "Organizations",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
