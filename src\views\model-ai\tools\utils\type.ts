export type FormItemProps = {
  id?: number | null;
  name?: string;
  slug?: string;
  description?: string;
  toolType?: string;
  version?: string;
  parameters?: Record<string, any>;
  configuration?: Record<string, any>;
  isActive?: boolean;
  status?: string;
  metadata?: Record<string, any>;
  categories?: number[];
};

export type ModelToolFilterProps = {
  name?: string;
  slug?: string;
  toolType?: string;
  isActive?: boolean;
  status?: string;
  categoryId?: number;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
