import { useEventListener } from "@vueuse/core";

/** Whether it's an `img` tag */
function isImgElement(element) {
  return typeof HTMLImageElement !== "undefined"
    ? element instanceof HTMLImageElement
    : element.tagName.toLowerCase() === "img";
}

// Import and call in src/main.ts: import { addPreventDefault } from "@/utils/preventDefault"; addPreventDefault();
export const addPreventDefault = () => {
  // Prevent opening browser developer tools via F12 keyboard shortcut
  useEventListener(
    window.document,
    "keydown",
    ev => ev.key === "F12" && ev.preventDefault()
  );
  // Prevent browser default right-click menu popup (won't affect custom right-click events)
  useEventListener(window.document, "contextmenu", ev => ev.preventDefault());
  // Prevent page element selection
  useEventListener(window.document, "selectstart", ev => ev.preventDefault());
  // Images in browsers are usually draggable by default and can be opened in new tabs or windows, or dragged to other applications. This disables that behavior to make them non-draggable by default
  useEventListener(
    window.document,
    "dragstart",
    ev => isImgElement(ev?.target) && ev.preventDefault()
  );
};
