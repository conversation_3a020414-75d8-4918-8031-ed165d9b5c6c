export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  name?: string;
  logo?: string;
  logoUrl?: string;
  description?: string;
  ownerId?: number;
  ownerType?: string;
  owner?: any;
  aiModelId?: number;
  aiModel?: any;
  systemPrompt?: string;
  greetingMessage?: string;
  starterMessages?: any;
  closingMessage?: string;
  parameters?: any;
  toolCallingMode?: "auto" | "none" | "required";
  visibility?: string;
  botType?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  metadata?: any;
  knowledge?: any;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
};

export type BotFilterProps = {
  name?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  visibility?: string;
  botType?: string;
  ownerId?: number;
  ownerType?: string;
  aiModelId?: number;
  toolCallingMode?: "auto" | "none" | "required";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};

// Safe Bot type for list display (without sensitive fields)
export type BotListItem = {
  uuid?: string;
  name?: string;
  logo?: string;
  description?: string;
  greetingMessage?: string;
  starterMessages?: any;
  closingMessage?: string;
  toolCallingMode?: "auto" | "none" | "required";
  visibility?: string;
  botType?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  createdAt?: string;
  updatedAt?: string;
  // Safe nested objects without IDs
  aiModel?: {
    name?: string;
    provider?: string;
  };
  owner?: {
    name?: string;
    avatar?: string;
  };
};

// File type utilities
import {
  Picture,
  VideoPlay,
  Headset,
  Files,
  Reading,
  DocumentCopy,
  Folder,
  Document
} from "@element-plus/icons-vue";

// Helper functions for file type display
export const getFileExtension = (filename: string): string => {
  return filename.split(".").pop()?.toLowerCase() || "";
};

export const getFileTypeIcon = (filename: string) => {
  const ext = getFileExtension(filename);

  // Image files
  if (["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"].includes(ext)) {
    return Picture;
  }

  // Video files
  if (["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"].includes(ext)) {
    return VideoPlay;
  }

  // Audio files
  if (["mp3", "wav", "flac", "aac", "ogg", "m4a"].includes(ext)) {
    return Headset;
  }

  // Document files
  if (["pdf", "doc", "docx", "txt", "rtf"].includes(ext)) {
    return Reading;
  }

  // Spreadsheet files
  if (["xls", "xlsx", "csv"].includes(ext)) {
    return DocumentCopy;
  }

  // Archive files
  if (["zip", "rar", "7z", "tar", "gz"].includes(ext)) {
    return Folder;
  }

  // Default
  return Document;
};

export const getFileTypeLabel = (filename: string): string => {
  const ext = getFileExtension(filename);

  if (!ext) return "File";

  const typeMap: Record<string, string> = {
    // Images
    jpg: "JPG",
    jpeg: "JPEG",
    png: "PNG",
    gif: "GIF",
    bmp: "BMP",
    webp: "WebP",
    svg: "SVG",

    // Videos
    mp4: "MP4",
    avi: "AVI",
    mov: "MOV",
    wmv: "WMV",
    flv: "FLV",
    webm: "WebM",
    mkv: "MKV",

    // Audio
    mp3: "MP3",
    wav: "WAV",
    flac: "FLAC",
    aac: "AAC",
    ogg: "OGG",
    m4a: "M4A",

    // Documents
    pdf: "PDF",
    doc: "DOC",
    docx: "DOCX",
    txt: "TXT",
    rtf: "RTF",

    // Spreadsheets
    xls: "XLS",
    xlsx: "XLSX",
    csv: "CSV",

    // Archives
    zip: "ZIP",
    rar: "RAR",
    "7z": "7Z",
    tar: "TAR",
    gz: "GZ"
  };

  return typeMap[ext] || ext.toUpperCase();
};

export const getFileTypeColor = (filename: string): string => {
  const ext = getFileExtension(filename);

  // Image files - green
  if (["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"].includes(ext)) {
    return "#67C23A";
  }

  // Video files - purple
  if (["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"].includes(ext)) {
    return "#9C27B0";
  }

  // Audio files - orange
  if (["mp3", "wav", "flac", "aac", "ogg", "m4a"].includes(ext)) {
    return "#FF9800";
  }

  // Document files - blue
  if (["pdf", "doc", "docx", "txt", "rtf"].includes(ext)) {
    return "#409EFF";
  }

  // Spreadsheet files - green
  if (["xls", "xlsx", "csv"].includes(ext)) {
    return "#67C23A";
  }

  // Archive files - brown
  if (["zip", "rar", "7z", "tar", "gz"].includes(ext)) {
    return "#8D6E63";
  }

  // Default - gray
  return "#909399";
};
