import { message } from "@/utils/message";
import { $t } from "@/plugins/i18n";

export const STORAGE_URL = (path: string): string => {
  if (!path) return "";

  if (path.startsWith("http") || path.startsWith("/")) {
    return path;
  }

  if (path.startsWith("data:image")) {
    return path;
  }

  return `/storage/${path}`;
};

export function useConvertKeyToCamel<T>(obj: any): T {
  if (!obj) return obj as T;

  const convertSnakeToCamel = (key: string): string =>
    key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());

  const convertObjectKeys = (o: any): any => {
    if (Array.isArray(o)) {
      return o.map(item => convertObjectKeys(item));
    } else if (o !== null && o !== undefined && o.constructor === Object) {
      return Object.keys(o).reduce((acc, key) => {
        const camelKey = convertSnakeToCamel(key);
        acc[camelKey] = convertObjectKeys(o[key]);
        return acc;
      }, {} as any);
    }
    return o;
  };

  return convertObjectKeys(obj);
}

export function useConvertKeyToSnake<T>(obj: any): T {
  if (!obj) return obj as T;

  const convertCamelToSnake = (key: string): string =>
    key.replace(/([A-Z])/g, match => "_" + match.toLowerCase());

  const convertObjectKeys = (o: any): any => {
    if (Array.isArray(o)) {
      return o.map(item => convertObjectKeys(item));
    } else if (o !== null && o !== undefined && o.constructor === Object) {
      return Object.keys(o).reduce((acc, key) => {
        const snakeKey = convertCamelToSnake(key);
        acc[snakeKey] = convertObjectKeys(o[key]);
        return acc;
      }, {} as any);
    }
    return o;
  };

  return convertObjectKeys(obj);
}

// Simple utility functions
export const useCamelKey = (key: string): string =>
  key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());

export const useSnakeKey = (key: string): string =>
  key.replace(/([A-Z])/g, match => "_" + match.toLowerCase());

export function handleError(error: any): void {
  const res = error?.response;
  message(res?.data?.message ?? res?.statusText ?? $t("common.unknownError"), {
    type: "error"
  });
}

export function handleSuccess(msg: string): void {
  message(msg, { type: "success" });
}

export function useAssignValues<T extends object>(
  source: any,
  target: T
): void {
  if (!source || !target) return;

  for (const key in source) {
    if (key in target && source[key] !== undefined) {
      (target as any)[key] = source[key];
    }
  }
}

// Alias for useAssignValues for backward compatibility
export const useMapOption = useAssignValues;

export function formatHigherMenuOptions(treeList: any[]): any[] {
  if (!treeList?.length) return [];

  return treeList.map(item => {
    const newItem = { ...item };
    if (newItem.title) {
      newItem.title = $t(newItem.title);
    }
    if (newItem.children?.length) {
      newItem.children = formatHigherMenuOptions(newItem.children);
    }
    return newItem;
  });
}

export function capitalized(text: string) {
  if (!text) return "";
  return text.charAt(0).toUpperCase() + text.slice(1);
}
