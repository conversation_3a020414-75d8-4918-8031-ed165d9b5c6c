<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getPermissionsDropdown } from "@/views/system/role/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const permissions = ref([]);
const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Role name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input role name"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Display Name")),
    prop: "displayName",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input display name"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: ""
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ]
  },
  {
    label: computed(() => $t("Permissions")),
    prop: "permissions",
    valueType: "cascader",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please choose permissions"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      props: {
        multiple: true,
        checkStrictly: false,
        emitPath: false
      },
      placeholder: "",
      filterable: true,
      collapseTags: true,
      collapseTagsTooltip: true,
      maxCollapseTags: 3,
      clearable: true
    },
    options: computed(() => [...permissions.value])
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

const getPermissions = async () => {
  try {
    const { data } = await getPermissionsDropdown();
    const camelCasedData = (useConvertKeyToCamel(data) || []) as any[];
    permissions.value = camelCasedData.map((group: any) => {
      // 1. Tạo một đối tượng mới cho mỗi group
      return {
        label: group.name,
        value: group.id,
        children:
          group.children?.map((permission: any) => {
            return {
              label: permission.name,
              value: permission.id
            };
          }) || []
      };
    });
  } catch (error) {
    permissions.value = [];
  }
};

watch(
  () => props.visible,
  async () => {
    if (props.visible) {
      await getPermissions();
    }
  }
);

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
