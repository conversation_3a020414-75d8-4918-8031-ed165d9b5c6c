import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getLanguages = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/languages", { params });
};

export const getLanguageById = (id: number) => {
  return http.request<Result>("get", `/api/v1/auth/languages/${id}`);
};

export const getLanguagesDropdown = () => {
  return http.request<Result>("get", "/api/v1/auth/languages/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createLanguage = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/languages", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateLanguageById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/languages/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteLanguage = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/languages/${id}/delete`);
};

export const bulkDeleteLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/languages/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteLanguagePermanent = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/languages/${id}/force`);
};

export const bulkDeleteLanguagesPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/languages/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreLanguage = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/languages/${id}/restore`);
};

export const bulkRestoreLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/v1/auth/languages/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
