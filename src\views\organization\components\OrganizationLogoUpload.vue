<script setup lang="ts">
import { ref } from "vue";
import { Plus, Loading } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import { ElMessage } from "element-plus";
import { uploadOrganizationLogo } from "../utils/auth-api";

interface Props {
  logoUrl?: string;
  autoUpload?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoUpload: true
});

const emit = defineEmits<{
  (e: "update:logoUrl", value: string): void;
  (e: "logoFileChange", file: File): void;
  (e: "uploadSuccess", response: any): void;
  (e: "uploadError", error: Error): void;
}>();

const uploading = ref(false);

const handleLogoChange = async (uploadFile: any) => {
  if (uploadFile.raw) {
    try {
      // Cleanup previous blob URL to prevent memory leak
      if (props.logoUrl && props.logoUrl.startsWith("blob:")) {
        URL.revokeObjectURL(props.logoUrl);
      }

      const logoUrl = URL.createObjectURL(uploadFile.raw);
      emit("update:logoUrl", logoUrl);
      emit("logoFileChange", uploadFile.raw);

      // Auto upload if enabled
      if (props.autoUpload) {
        await handleAutoUpload(uploadFile.raw);
      }
    } catch (error) {
      console.error("Error handling logo change:", error);
      ElMessage.error($t("Failed to process selected file"));
    }
  }
};

const handleAutoUpload = async (file: File) => {
  try {
    uploading.value = true;
    const response = await uploadOrganizationLogo(file);

    if (response.success && response.data) {
      // Update logoUrl with server response
      const serverLogoUrl = response.data.logo_url;
      if (serverLogoUrl) {
        emit("update:logoUrl", serverLogoUrl);
      }

      emit("uploadSuccess", response.data);
      ElMessage.success($t("Logo uploaded successfully"));
    }
  } catch (error: any) {
    console.error("Logo upload failed:", error);
    emit("uploadError", error);
    ElMessage.error(
      $t("Logo upload failed: {error}", {
        error: error.message || $t("Unknown error")
      })
    );
  } finally {
    uploading.value = false;
  }
};

// File validation
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith("image/");
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error($t("Only image files are allowed!"));
    return false;
  }
  if (!isLt5M) {
    ElMessage.error($t("Image size must be less than 5MB!"));
    return false;
  }
  return true;
};
</script>

<template>
  <div class="logo-upload-section mx-auto">
    <div class="flex justify-center mb-4">
      <el-upload
        class="logo-uploader"
        action="#"
        :show-file-list="false"
        :auto-upload="false"
        accept="image/*"
        :before-upload="beforeUpload"
        :disabled="uploading"
        @change="handleLogoChange"
      >
        <div v-if="uploading" class="upload-loading">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span class="loading-text">{{ $t("Uploading...") }}</span>
        </div>
        <img
          v-else-if="props.logoUrl"
          :src="props.logoUrl"
          class="logo-preview"
          alt="organization logo"
        />
        <el-icon v-else class="logo-uploader-icon">
          <Plus />
        </el-icon>
      </el-upload>
    </div>
    <p class="text-xs text-center text-gray-500">
      {{ $t("Upload JPG, PNG, JPEG images. Size under 5MB.") }}
    </p>
    <p v-if="props.autoUpload" class="text-xs text-center text-blue-500 mt-1">
      {{ $t("Files will be uploaded automatically") }}
    </p>
  </div>
</template>

<style scoped>
.logo-upload-section {
  margin-bottom: 20px;
}

.logo-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-uploader:hover {
  border-color: #409eff;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-preview {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  color: #409eff;
}

.upload-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.loading-text {
  font-size: 12px;
  color: #606266;
}
</style>
