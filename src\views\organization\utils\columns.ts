import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElAvatar } from "element-plus";
import { h } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    prop: "logo",
    align: "center",
    width: 90,
    headerRenderer: () => $t("Logo"),
    cellRenderer: ({ row }) => {
      return h(ElAvatar, {
        size: 50,
        src: row.logoUrl,
        alt: row.name
      });
    }
  },
  {
    prop: "name",
    align: "left",
    sortable: false,
    minWidth: 210,
    headerRenderer: () => $t("Organization Name"),
    cellRenderer: ({ row }) => {
      return h("div", { class: "flex flex-col" }, [
        h(
          "div",
          {
            class: "font-medium text-gray-900 text-sm line-clamp-2"
          },
          row.name || $t("Untitled Organization")
        ),
        h(
          "div",
          {
            class: "text-sm text-gray-500 mt-1"
          },
          row.description || row.email
        )
      ]);
    }
  },
  {
    prop: "organizationType",
    align: "left",
    width: 130,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => {
      const typeColors = {
        company: {
          class: "bg-blue-100 text-blue-800",
          icon: "ri:building-line",
          iconClass: "text-blue-600"
        },
        nonprofit: {
          class: "bg-green-100 text-green-800",
          icon: "ri:heart-line",
          iconClass: "text-green-600"
        },
        government: {
          class: "bg-purple-100 text-purple-800",
          icon: "ri:government-line",
          iconClass: "text-purple-600"
        },
        educational: {
          class: "bg-orange-100 text-orange-800",
          icon: "ri:school-line",
          iconClass: "text-orange-600"
        }
      };

      const config = typeColors[row.organizationType] || typeColors.company;
      const displayText = row.organizationType
        ? row.organizationType.charAt(0).toUpperCase() +
          row.organizationType.slice(1)
        : $t("Company");

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          displayText
        ]
      );
    }
  },
  {
    prop: "status",
    align: "left",
    width: 130,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        active: {
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600",
          text: $t("Active")
        },
        inactive: {
          class: "bg-gray-100 text-gray-800",
          icon: "ri:pause-circle-line",
          iconClass: "text-gray-600",
          text: $t("Inactive")
        },
        suspended: {
          class: "bg-red-100 text-red-800",
          icon: "ri:close-circle-fill",
          iconClass: "text-red-600",
          text: $t("Suspended")
        }
      };

      const config = statusColors[row.status] || statusColors.active;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    prop: "createdAt",
    align: "left",
    width: 140,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) => {
      return h(
        "span",
        {
          class: "text-sm text-gray-600"
        },
        dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
      );
    }
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
