import type { RouteRecordName } from "vue-router";

export type cacheType = {
  mode: string;
  name?: RouteRecordName;
};

export type positionType = {
  startIndex?: number;
  length?: number;
};

export type appType = {
  sidebar: {
    opened: boolean;
    withoutAnimation: boolean;
    // Determine if manually clicked collapse
    isClickCollapse: boolean;
  };
  layout: string;
  device: string;
  viewportSize: { width: number; height: number };
};

export type multiType = {
  path: string;
  name: string;
  meta: any;
  query?: object;
  params?: object;
};

export type setType = {
  title: string;
  fixedHeader: boolean;
  hiddenSideBar: boolean;
  settings: Record<string, any>;
};

export type userType = {
  id?: number;
  username?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  email?: string;
  avatar?: string;
  avatarUrl?: string;
  birthday?: string;
  gender?: string;
  phone?: string;
  address?: string;
  status?: string;
  lastLoginAt?: string;
  lastLoginIp?: string;
  preferences?: any;
  isVerified?: boolean;
  newsletterSubscribed?: boolean;
  createdAt?: string;
  updatedAt?: string;
  nickname?: string;
  roles?: Array<string>;
  permissions?: Array<string>;
  isRemembered?: boolean;
  loginDay?: number;
  country?: any;
};
