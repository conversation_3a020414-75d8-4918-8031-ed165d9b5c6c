<script setup lang="ts">
import { computed, ref, watch } from "vue";
import {
  <PERSON><PERSON><PERSON>on,
  ElCard,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElSwitch
} from "element-plus";
import { Delete, Plus, Setting } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

// ==== Types ====
interface Parameter {
  key: string;
  type: "number" | "string" | "boolean";
  value: any;
  min?: number;
  max?: number;
  step?: number;
  description: string;
}

// ==== Props & Emits ====
const props = defineProps<{
  defaultParameters?: Record<string, any>;
  allowedParameters?: string[];
}>();

const emit = defineEmits<{
  (e: "update:defaultParameters", value: Record<string, any>): void;
  (e: "update:allowedParameters", value: string[]): void;
}>();

// ==== Template Data ====
const templates: Parameter[] = [
  {
    key: "temperature",
    type: "number",
    min: 0,
    max: 2,
    step: 0.1,
    value: 0.7,
    description: "Controls creativity"
  },
  {
    key: "max_tokens",
    type: "number",
    min: 1,
    max: 8192,
    step: 1,
    value: 1000,
    description: "Maximum tokens"
  },
  {
    key: "top_p",
    type: "number",
    min: 0,
    max: 1,
    step: 0.01,
    value: 0.9,
    description: "Nucleus sampling"
  },
  {
    key: "frequency_penalty",
    type: "number",
    min: -2,
    max: 2,
    step: 0.1,
    value: 0,
    description: "Frequency penalty"
  },
  {
    key: "presence_penalty",
    type: "number",
    min: -2,
    max: 2,
    step: 0.1,
    value: 0,
    description: "Presence penalty"
  },
  {
    key: "stop_sequences",
    type: "string",
    value: "[]",
    description: "Stop sequences (JSON array)"
  }
];

// ==== Refs & State ====
const defaultParams = ref<Parameter[]>([]);
const allowedParams = ref<Parameter[]>([]);
const selectedTemplate = ref("");
const selectedDefaultParam = ref("");
const isInternalUpdate = ref(false);

// ==== Utility ====
const getTemplate = (key: string): Parameter | undefined =>
  templates.find(t => t.key === key);

const toParameter = (key: string, value: any): Parameter => {
  const t = getTemplate(key);
  return {
    key,
    value,
    type: t?.type || "string",
    min: t?.min,
    max: t?.max,
    step: t?.step,
    description: t?.description || ""
  };
};

// ==== Computed ====
const availableTemplates = computed(() =>
  templates.filter(t => !defaultParams.value.some(p => p.key === t.key))
);

const availableDefaultParams = computed(() =>
  defaultParams.value.filter(
    p => !allowedParams.value.some(ap => ap.key === p.key)
  )
);

// ==== Actions ====
const addToDefault = () => {
  const template = getTemplate(selectedTemplate.value);
  if (!template) return;
  defaultParams.value.push({ ...template });
  selectedTemplate.value = "";
  emitUpdate();
};

const addToAllowed = () => {
  const param = defaultParams.value.find(
    p => p.key === selectedDefaultParam.value
  );
  if (!param) return;
  allowedParams.value.push({ ...param });
  selectedDefaultParam.value = "";
  emitUpdate();
};

const removeFromDefault = (key: string) => {
  console.log("removeFromDefault called with key:", key);
  console.log("allowedParams before remove:", allowedParams.value);

  const index = defaultParams.value.findIndex(param => param.key === key);
  if (index !== -1) {
    // Remove from defaultParams
    defaultParams.value.splice(index, 1);

    // Remove from allowedParams since it's no longer available
    const allowedIndex = allowedParams.value.findIndex(p => p.key === key);
    if (allowedIndex !== -1) {
      allowedParams.value.splice(allowedIndex, 1);
    }

    console.log("allowedParams after remove:", allowedParams.value);
    emitUpdate();
  }
};

const removeFromAllowed = (index: number) => {
  if (index >= 0 && index < allowedParams.value.length) {
    allowedParams.value.splice(index, 1);
    emitUpdate();
  }
};

const updateDefaultValue = (index: number, value: any) => {
  const param = defaultParams.value[index];
  if (!param) return;

  param.value = value;
  const allowed = allowedParams.value.find(p => p.key === param.key);
  if (allowed) allowed.value = value;
  emitUpdate();
};

const emitUpdate = () => {
  // Set flag to indicate this is an internal update
  isInternalUpdate.value = true;

  // Create objects from current state
  const defaultParamsObject = Object.fromEntries(
    defaultParams.value.filter(p => p && p.key).map(p => [p.key, p.value])
  );

  const allowedParamsArray = allowedParams.value
    .filter(p => p && p.key)
    .map(p => p.key);

  emit("update:defaultParameters", defaultParamsObject);
  emit("update:allowedParameters", allowedParamsArray);

  // Reset flag after a short delay
  setTimeout(() => {
    isInternalUpdate.value = false;
  }, 200);
};

watch(
  () => props.defaultParameters,
  value => {
    if (isInternalUpdate.value) {
      return;
    }

    if (!value || typeof value !== "object") {
      defaultParams.value = [];
      return;
    }

    defaultParams.value = Object.entries(value)
      .filter(([key, val]) => key && val !== undefined && val !== null)
      .map(([key, val]) => toParameter(key, val));
  },
  { immediate: true }
);

// Init allowedParams
watch(
  () => props.allowedParameters,
  allowedKeys => {
    if (isInternalUpdate.value) {
      return;
    }

    if (!allowedKeys || !Array.isArray(allowedKeys)) {
      allowedParams.value = [];
      return;
    }

    // Build allowedParams from the keys, finding corresponding parameters
    allowedParams.value = allowedKeys
      .filter(key => key && typeof key === "string")
      .map(key => {
        let param = defaultParams.value.find(p => p.key === key);
        if (!param) {
          const template = getTemplate(key);
          if (template) {
            param = { ...template };
          }
        }
        return param;
      })
      .filter(Boolean) as Parameter[];
  },
  { immediate: true }
);
</script>

<template>
  <div class="space-y-4 w-full">
    <!-- Add to Default -->
    <div class="flex flex-col gap-2">
      <div class="flex gap-3">
        <ElSelect
          v-model="selectedTemplate"
          placeholder="Select parameter to add"
          class="flex-1"
        >
          <ElOption
            v-for="t in availableTemplates"
            :key="t.key"
            :label="`${t.key} - ${t.description}`"
            :value="t.key"
          />
        </ElSelect>
        <ElButton
          :icon="Plus"
          type="primary"
          :disabled="!selectedTemplate"
          @click="addToDefault"
        >
          Add to Default
        </ElButton>
      </div>
      <!-- Parameter Info -->
      <div
        v-if="selectedTemplate && getTemplate(selectedTemplate)"
        class="bg-blue-50 p-3 rounded-lg"
      >
        <div class="flex items-center space-x-2 mb-2">
          <ElTag
            :type="
              getTemplate(selectedTemplate)?.type === 'number'
                ? 'warning'
                : getTemplate(selectedTemplate)?.type === 'boolean'
                  ? 'success'
                  : 'info'
            "
          >
            {{ getTemplate(selectedTemplate)?.type }}
          </ElTag>
          <span class="font-medium">
            {{ getTemplate(selectedTemplate)?.key }}
          </span>
        </div>
        <p class="text-sm text-gray-600 mb-2">
          {{ getTemplate(selectedTemplate)?.description }}
        </p>
        <div
          v-if="getTemplate(selectedTemplate)?.type === 'number'"
          class="text-xs text-gray-500"
        >
          Range: {{ getTemplate(selectedTemplate)?.min }} -
          {{ getTemplate(selectedTemplate)?.max }}
          <span v-if="getTemplate(selectedTemplate)?.step">
            , Step: {{ getTemplate(selectedTemplate)?.step }}
          </span>
        </div>
      </div>
    </div>

    <!-- Default Parameters -->
    <ElCard>
      <template #header>
        <div class="flex items-center gap-2">
          <ElIcon><Setting /></ElIcon>
          <span>Default Parameters ({{ defaultParams.length }})</span>
        </div>
      </template>

      <div
        v-if="defaultParams.length === 0"
        class="text-gray-500 py-4 text-center"
      >
        {{ $t("No default parameters") }}
      </div>

      <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="(param, index) in defaultParams"
          :key="param.key"
          class="px-1 border rounded bg-gray-50 flex flex-col gap-2"
        >
          <div class="flex justify-between items-center">
            <div class="font-medium">{{ param.key }}</div>
            <div>
              <ElInputNumber
                v-if="param.type === 'number'"
                :model-value="param.value"
                :min="param.min"
                :max="param.max"
                :step="param.step"
                size="small"
                class="w-full"
                @update:model-value="val => updateDefaultValue(index, val)"
              />

              <ElSwitch
                v-else-if="param.type === 'boolean'"
                :model-value="param.value"
                @update:model-value="val => updateDefaultValue(index, val)"
              />

              <ElInput
                v-else
                :model-value="param.value"
                size="small"
                class="w-full"
                @update:model-value="val => updateDefaultValue(index, val)"
              />
            </div>
            <ElButton
              type="danger"
              :icon="Delete"
              size="small"
              circle
              @click="removeFromDefault(param.key)"
            />
          </div>
        </div>
      </div>
    </ElCard>

    <!-- Add to Allowed -->
    <div class="flex gap-3">
      <ElSelect
        v-model="selectedDefaultParam"
        placeholder="Select from Default to allow"
        class="flex-1"
      >
        <ElOption
          v-for="param in availableDefaultParams"
          :key="param.key"
          :label="param.key"
          :value="param.key"
        />
      </ElSelect>
      <ElButton
        type="success"
        :icon="Plus"
        :disabled="!selectedDefaultParam"
        @click="addToAllowed"
      >
        Add to Allowed
      </ElButton>
    </div>

    <!-- Allowed Parameters -->
    <ElCard>
      <template #header>
        <div class="flex items-center gap-2">
          <ElIcon><Setting /></ElIcon>
          <span>Allowed Parameters ({{ allowedParams.length }})</span>
        </div>
      </template>

      <div
        v-if="allowedParams.length === 0"
        class="text-gray-500 text-center py-4"
      >
        No allowed parameters
      </div>

      <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="(param, index) in allowedParams"
          :key="param.key"
          class="p-4 border rounded bg-green-50 flex justify-between items-start"
        >
          <div class="font-medium text-green-900">{{ param.key }}</div>
          <ElButton
            type="danger"
            :icon="Delete"
            size="small"
            circle
            @click="removeFromAllowed(index)"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
/* Optional styling */
</style>
