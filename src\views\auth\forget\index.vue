<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { forgotPasswordRules } from "./utils/rule";
import { ref, reactive, toRaw } from "vue";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import Lock from "@iconify-icons/ri/lock-fill";
import TypeIt from "@/components/ReTypeit/src";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import AuthNavigation from "../components/AuthNavigation.vue";

defineOptions({
  name: "ForgetPassword"
});

const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();

const imgCode = ref("");

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title } = useNav();

const ruleForm = reactive({
  email: ""
});

const onForgotPassword = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    loading.value = true;
    const res: Result = await useUserStoreHook().forgotPassword({
      email: ruleForm.email
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    message(res.message, { type: "success" });
    // Redirect to reset password page with email
    await router.push({
      path: "/reset-password",
      query: { email: ruleForm.email }
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Some information is incorrect. Please review and try again."),
      {
        type: "error"
      }
    );
  } finally {
    loading.value = false;
  }
};

const immediateDebounce: any = debounce(
  formRef => onForgotPassword(formRef),
  1000,
  true
);

const goToLogin = () => {
  router.push("/login");
};

const goToRegister = () => {
  router.push("/register");
};

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" alt="Bg" />
    <div class="flex-c absolute right-5 top-3">
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">
              <TypeIt
                :options="{
                  strings: [$t('Forgot Password')],
                  cursor: false,
                  speed: 100
                }"
              />
            </h2>
          </Motion>

          <div class="text-center mb-4 text-gray-500">
            {{
              $t(
                "Enter your email address and we'll send you an OTP code to reset your password"
              )
            }}
          </div>
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="forgotPasswordRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: $t('Please enter email'),
                    trigger: 'blur'
                  }
                ]"
                prop="email"
              >
                <el-input
                  v-model="ruleForm.email"
                  clearable
                  :placeholder="$t('Email')"
                  :prefix-icon="useRenderIcon('ri:mail-line')"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <div class="flex justify-between items-center">
                <el-button
                  class="w-full !uppercase"
                  size="large"
                  type="danger"
                  round
                  :loading="loading"
                  :disabled="disabled"
                  @click="onForgotPassword(ruleFormRef)"
                >
                  <IconifyIconOnline
                    :icon="'ri:login-box-line'"
                    width="20"
                    class="mr-2"
                  />
                  {{ $t("Send OTP Code") }}
                </el-button>
              </div>
            </Motion>

            <Motion :delay="300">
              <AuthNavigation
                :show-login="true"
                :show-register="true"
                :show-forgot-password="false"
              />
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
