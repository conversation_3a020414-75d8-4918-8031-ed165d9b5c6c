import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const organizationRules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter the organization name"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 255,
      message: $t("Length must be between 2 and 255 characters"),
      trigger: "blur"
    }
  ],
  email: [
    {
      required: true,
      message: $t("Please enter the email"),
      trigger: "blur"
    },
    {
      type: "email",
      message: $t("Please enter a valid email address"),
      trigger: "blur"
    }
  ],
  organizationType: [
    {
      required: true,
      message: $t("Please select organization type"),
      trigger: "change"
    }
  ],
  status: [
    {
      required: true,
      message: $t("Please select status"),
      trigger: "change"
    }
  ]
});

export { organizationRules };
