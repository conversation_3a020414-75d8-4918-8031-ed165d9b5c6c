<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, onMounted } from "vue";
import { getCategoriesDropdown } from "@/views/model-ai/category/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();
const categories = ref([]);

onMounted(async () => {
  try {
    const { data } = await getCategoriesDropdown();
    categories.value = useConvertKeyToCamel(data);
  } catch (error) {
    console.error("Failed to load categories:", error);
  }
});

const toolTypes = [
  { label: $t("Function"), value: "function" },
  { label: $t("Plugin"), value: "plugin" },
  { label: $t("Integration"), value: "integration" },
  { label: $t("Custom"), value: "custom" }
];

const statusOptions = [
  { label: $t("Active"), value: "active" },
  { label: $t("Inactive"), value: "inactive" }
];

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Tool Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Search by tool name"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Slug")),
    prop: "slug",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Search by slug"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Tool Type")),
    prop: "toolType",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select tool type"),
      clearable: true
    },
    options: toolTypes,
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Category")),
    prop: "categoryId",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select category"),
      clearable: true
    },
    options: computed(() =>
      categories.value.map(cat => ({
        label: cat.name,
        value: cat.id
      }))
    ),
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select status"),
      clearable: true
    },
    options: statusOptions,
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Active Status")),
    prop: "isActive",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select active status"),
      clearable: true
    },
    options: [
      { label: $t("Active"), value: true },
      { label: $t("Inactive"), value: false }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Trash Status")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select trash status"),
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "no" },
      { label: $t("Trashed"), value: "yes" }
    ],
    colProps: { span: 12 }
  }
];

const handleSubmit = async () => {
  try {
    loading.value = true;
    emit("submit", props.values);
    emit("update:visible", false);
  } catch (error) {
    console.error("Filter submission failed:", error);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  emit("reset");
  emit("update:visible", false);
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter Tools") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}

.custom-footer {
  @apply flex justify-end gap-2;
}
</style>
