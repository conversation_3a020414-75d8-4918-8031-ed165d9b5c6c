import { defineStore } from "pinia";
import { store } from "@/store";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { getTranslations, getTranslationGroups } from "@/views/translation/utils/auth-api";

export interface Translation {
  id: number;
  key: string;
  languageCode: string;
  value: string;
}

interface TranslationState {
  translations: Translation[] | null;
  groups: any[] | null;
}

export const useTranslationStore = defineStore("proCMS-translation", {
  state: (): TranslationState => {
    return {
      translations: null,
      groups: null
    };
  },
  getters: {
    getTranslations: state => state.translations,
    getGroups: state => state.groups
  },
  actions: {
    setTranslations(data: any) {
      this.translations = data;
    },
    setGroups(data: any) {
      this.groups = data;
    },
    async fetchTranslations(params?: object) {
      try {
        const { data } = await getTranslations(params);
        this.setTranslations(useConvertKeyToCamel(data));
      } catch (error) {
        console.error("Failed to fetch translations:", error);
      }
    },
    async fetchGroups() {
      try {
        const { data } = await getTranslationGroups();
        this.setGroups(useConvertKeyToCamel(data));
      } catch (error) {
        console.error("Failed to fetch groups:", error);
      }
    }
  }
});

export function useTranslationStoreHook() {
  return useTranslationStore(store);
}
