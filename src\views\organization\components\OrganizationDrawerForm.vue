<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, onMounted, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import OrganizationLogoUpload from "./OrganizationLogoUpload.vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Logo")),
    prop: "logo",
    valueType: "",
    renderField: () => {
      return h(OrganizationLogoUpload, {
        logoUrl: String(props.values.logoUrl || ""),
        autoUpload: true,
        "onUpdate:logoUrl": val => {
          if (props.values) {
            // eslint-disable-next-line vue/no-mutating-props
            props.values.logoUrl = val;
          }
        },
        onLogoFileChange: file => {
          if (props.values) {
            // eslint-disable-next-line vue/no-mutating-props
            props.values.logo = file;
          }
        },
        onUploadSuccess: response => {
          // Handle successful upload
          console.log("Logo uploaded successfully:", response);
        },
        onUploadError: error => {
          // Handle upload error
          console.error("Logo upload failed:", error);
        }
      });
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Registration Type")),
    prop: "type",
    valueType: "select",
    options: [
      { label: $t("Team"), value: "team" },
      { label: $t("Company"), value: "company" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false,
      style: { width: "100%" }
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Organization Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input organization name"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length must be between 2 and 100 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Email")),
    prop: "email",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input email"),
        trigger: ["blur"]
      },
      {
        type: "email",
        message: $t("Please input valid email"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Suspended"), value: "suspended" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    colProps: { span: 9 }
  },
  {
    label: computed(() => $t("Website")),
    prop: "website",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Is Verified")),
    prop: "isVerified",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Yes"),
      inactiveText: $t("No")
    },
    colProps: { span: 9 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Organization Information") }}
        </span>
      </div>
    </template>

    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(props.values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
