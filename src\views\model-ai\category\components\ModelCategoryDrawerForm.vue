<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { IconSelect } from "@/components/ReIcon";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Category key")),
    prop: "key",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input category key"),
        trigger: ["blur"]
      },
      {
        pattern: /^[a-z0-9-_]+$/,
        message: $t(
          "Key can only contain letters, numbers, dots, underscores and hyphens"
        ),
        trigger: "blur"
      }
    ],
    fieldProps: {
      placeholder: $t("e.g., text-generation, image-analysis")
    },
    colProps: { span: 17 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    colProps: { span: 7 }
  },
  {
    label: computed(() => $t("Category name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input category name"),
        trigger: ["blur", "change"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length should be between 2 and 100 characters"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Category type")),
    prop: "type",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Model AI"), value: "ModelAI" },
      { label: $t("Tools"), value: "Tools" }
    ],
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Category Icon")),
    prop: "icon",
    valueType: "",
    renderField: () =>
      h(IconSelect, {
        // @ts-ignore
        modelValue: props?.values?.icon,
        "onUpdate:modelValue": val => {
          if (props?.values) {
            // eslint-disable-next-line vue/no-mutating-props
            props.values.icon = val;
          }
        },
        class: "w-full"
      }),
    fieldProps: { class: "w-full" },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Color")),
    prop: "color",
    valueType: "color-picker",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Sort Order")),
    prop: "sortOrder",
    valueType: "input-number",
    fieldProps: {
      placeholder: "",
      min: 0,
      max: 9999,
      step: 1
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 6 }
    },
    colProps: { span: 24 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
