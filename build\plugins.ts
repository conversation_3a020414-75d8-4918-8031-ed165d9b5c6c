import { cdn } from "./cdn";
import vue from "@vitejs/plugin-vue";
import { viteBuildInfo } from "./info";
import svgLoader from "vite-svg-loader";
import Icons from "unplugin-icons/vite";
import type { PluginOption } from "vite";
import vueJsx from "@vitejs/plugin-vue-jsx";
import tailwindcss from "@tailwindcss/vite";
import { configCompressPlugin } from "./compress";
import removeNoMatch from "vite-plugin-router-warn";
import { visualizer } from "rollup-plugin-visualizer";
import removeConsole from "vite-plugin-remove-console";
import { codeInspectorPlugin } from "code-inspector-plugin";


export function getPluginsList(
  VITE_CDN: boolean,
  VITE_COMPRESSION: ViteCompression
): PluginOption[] {
  const lifecycle = process.env.npm_lifecycle_event;
  return [
    tailwindcss(),
    vue(),
    // jsx, tsx syntax support
    vueJsx(),
    /**
     * When holding down the key combination on the page, moving the mouse on the page will display a mask layer on the DOM and show related information. Clicking once will automatically open the IDE and position the cursor to the code location corresponding to the element
     * Mac default key combination: Option + Shift
     * Windows default key combination: Alt + Shift
     * More usage see https://inspector.fe-dev.cn/guide/start.html
     */
    codeInspectorPlugin({
      bundler: "vite",
      hideConsole: true
    }),
    viteBuildInfo(),
    /**
     * Remove unnecessary vue-router dynamic route warnings "No match found for location with path" in development environment
     * For details see https://github.com/vuejs/router/issues/521 and https://github.com/vuejs/router/issues/359
     * vite-plugin-router-warn is only enabled in development environment, only processes vue-router files and only runs once when the service starts or restarts, performance consumption is negligible
     */
    removeNoMatch(),

    // svg component support
    svgLoader(),
    // Auto on-demand loading of icons
    Icons({
      compiler: "vue3",
      scale: 1
    }),
    VITE_CDN ? cdn : null,
    configCompressPlugin(VITE_COMPRESSION),
    // Remove console in production environment
    removeConsole({ external: ["src/assets/iconfont/iconfont.js"] }),
    // Build analysis
    lifecycle === "report"
      ? visualizer({ open: true, brotliSize: true, filename: "report.html" })
      : (null as any)
  ];
}
