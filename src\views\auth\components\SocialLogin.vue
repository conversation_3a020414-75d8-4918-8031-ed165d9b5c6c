<script setup lang="ts">
import { ref } from "vue";
import { message } from "@/utils/message";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { getSocialLoginUrl } from "../utils/common";

interface Props {
  providers?: string[];
  loading?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  providers: () => ["facebook", "google", "twitter"],
  loading: false,
  disabled: false
});

const emit = defineEmits<{
  socialLogin: [provider: string];
}>();

const socialLoading = ref<Record<string, boolean>>({});

const providerConfig = {
  facebook: {
    icon: "logos:facebook",
    name: "Facebook",
    color: "#1877f2"
  },
  google: {
    icon: "flat-color-icons:google",
    name: "Google",
    color: "#4285f4"
  },
  twitter: {
    icon: "ant-design:twitter-circle-filled",
    name: "Twitter",
    color: "#1da1f2"
  },
  github: {
    icon: "akar-icons:github-fill",
    name: "GitH<PERSON>",
    color: "#333"
  },
  linkedin: {
    icon: "akar-icons:linkedin-fill",
    name: "LinkedIn",
    color: "#0077b5"
  }
};

const handleSocialLogin = async (provider: string) => {
  if (props.disabled || props.loading || socialLoading.value[provider]) {
    return;
  }

  try {
    socialLoading.value[provider] = true;

    // Emit event to parent component
    emit("socialLogin", provider);

    // Alternative: Direct redirect approach
    // const redirectUrl = getSocialLoginUrl(provider);
    // window.location.href = redirectUrl;
  } catch (error: any) {
    message(error?.message || $t("Social login failed"), {
      type: "error"
    });
  } finally {
    socialLoading.value[provider] = false;
  }
};

const getProviderConfig = (provider: string) => {
  return (
    providerConfig[provider as keyof typeof providerConfig] || {
      icon: "ri:user-line",
      name: provider,
      color: "#666"
    }
  );
};
</script>

<template>
  <div class="social-login">
    <el-divider>
      <p class="text-gray-500 text-xs">
        {{ $t("Or continue with") }}
      </p>
    </el-divider>

    <div class="w-full flex justify-evenly gap-4">
      <el-tooltip
        v-for="provider in providers"
        :key="provider"
        :content="
          $t('Continue with {provider}', {
            provider: getProviderConfig(provider).name
          })
        "
        placement="top"
      >
        <el-button
          :loading="socialLoading[provider]"
          :disabled="disabled || loading"
          class="social-button"
          size="large"
          circle
          @click="handleSocialLogin(provider)"
        >
          <IconifyIconOnline
            :icon="getProviderConfig(provider).icon"
            width="24"
            height="24"
          />
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<style scoped>
.social-login {
  margin-top: 1rem;
}

.social-button {
  width: 48px;
  height: 48px;
  border: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
}

.social-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.social-button:active {
  transform: translateY(0);
}

.dark .social-button {
  background: #374151;
  border-color: #4b5563;
}

.dark .social-button:hover {
  border-color: #6b7280;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style>
