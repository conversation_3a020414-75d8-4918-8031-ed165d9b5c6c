/**
 * API Utilities for Widget
 * Helper functions for API operations, error handling, and data transformation
 */

import { ApiError } from "./bot-api";
import type { ApiResponse } from "../types";

/**
 * Rate limiter for API requests
 */
export class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests = 100, timeWindowMs = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindowMs;
  }

  canMakeRequest(): boolean {
    const now = Date.now();

    // Remove old requests outside time window
    this.requests = this.requests.filter(time => now - time < this.timeWindow);

    return this.requests.length < this.maxRequests;
  }

  recordRequest(): void {
    this.requests.push(Date.now());
  }

  getRequestsRemaining(): number {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    return Math.max(0, this.maxRequests - this.requests.length);
  }

  getResetTime(): number {
    if (this.requests.length === 0) return 0;
    return this.requests[0] + this.timeWindow;
  }
}

/**
 * Request queue for managing concurrent requests
 */
export class RequestQueue {
  private queue: Array<() => Promise<any>> = [];
  private processing = false;
  private maxConcurrent: number;
  private currentRequests = 0;

  constructor(maxConcurrent = 5) {
    this.maxConcurrent = maxConcurrent;
  }

  async add<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.currentRequests >= this.maxConcurrent) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0 && this.currentRequests < this.maxConcurrent) {
      const request = this.queue.shift();
      if (request) {
        this.currentRequests++;
        request().finally(() => {
          this.currentRequests--;
          this.processQueue();
        });
      }
    }

    this.processing = false;
  }

  getQueueSize(): number {
    return this.queue.length;
  }

  clear(): void {
    this.queue.length = 0;
  }
}

/**
 * API Response validator
 */
export function validateApiResponse<T>(response: any): ApiResponse<T> {
  if (typeof response !== "object" || response === null) {
    throw new Error("Invalid API response: not an object");
  }

  if (typeof response.success !== "boolean") {
    throw new Error("Invalid API response: missing success field");
  }

  if (!response.success && !response.message) {
    throw new Error("Invalid API response: missing error message");
  }

  return response as ApiResponse<T>;
}

/**
 * Error handler for API responses
 */
export function handleApiError(error: any): never {
  if (error instanceof ApiError) {
    throw error;
  }

  if (error instanceof TypeError && error.message.includes("fetch")) {
    throw new ApiError(
      0,
      "Network Error",
      "Network connection failed. Please check your internet connection."
    );
  }

  if (error.name === "AbortError") {
    throw new ApiError(408, "Timeout", "Request timeout. Please try again.");
  }

  throw new ApiError(
    500,
    "Unknown Error",
    error.message || "An unexpected error occurred"
  );
}

/**
 * Retry utility with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxAttempts = 3,
  baseDelay = 1000,
  maxDelay = 10000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      // Don't retry on client errors (4xx)
      if (
        error instanceof ApiError &&
        error.status >= 400 &&
        error.status < 500
      ) {
        throw error;
      }

      if (attempt === maxAttempts - 1) {
        break;
      }

      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Data transformation utilities
 */
export const DataTransform = {
  /**
   * Convert snake_case to camelCase
   */
  toCamelCase(obj: any): any {
    if (obj === null || typeof obj !== "object") return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => this.toCamelCase(item));
    }

    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase()
      );
      result[camelKey] = this.toCamelCase(value);
    }
    return result;
  },

  /**
   * Convert camelCase to snake_case
   */
  toSnakeCase(obj: any): any {
    if (obj === null || typeof obj !== "object") return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => this.toSnakeCase(item));
    }

    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = key.replace(
        /[A-Z]/g,
        letter => `_${letter.toLowerCase()}`
      );
      result[snakeKey] = this.toSnakeCase(value);
    }
    return result;
  },

  /**
   * Sanitize user input
   */
  sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, "") // Remove potential HTML tags
      .substring(0, 4000); // Limit length
  },

  /**
   * Format file size
   */
  formatFileSize(bytes: number): string {
    const units = ["B", "KB", "MB", "GB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  },

  /**
   * Validate file type
   */
  isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.some(type => {
      if (type.includes("/")) {
        return file.type === type;
      }
      return file.type.startsWith(`${type}/`);
    });
  }
};

/**
 * URL utilities
 */
export const UrlUtils = {
  /**
   * Build URL with query parameters
   */
  buildUrl(
    baseUrl: string,
    path: string,
    params?: Record<string, any>
  ): string {
    const url = new URL(path, baseUrl);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value));
        }
      });
    }

    return url.toString();
  },

  /**
   * Extract domain from URL
   */
  extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return "";
    }
  },

  /**
   * Check if URL is same origin
   */
  isSameOrigin(url: string): boolean {
    if (typeof window === "undefined") return false;

    try {
      const urlObj = new URL(url, window.location.origin);
      return urlObj.origin === window.location.origin;
    } catch {
      return false;
    }
  }
};

/**
 * Storage utilities for caching
 */
export const StorageUtils = {
  /**
   * Safe localStorage operations
   */
  setItem(key: string, value: any, ttl?: number): boolean {
    try {
      const item = {
        value,
        timestamp: Date.now(),
        ttl: ttl || 0
      };
      localStorage.setItem(key, JSON.stringify(item));
      return true;
    } catch {
      return false;
    }
  },

  getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key);
      if (!item) return null;

      const parsed = JSON.parse(item);

      // Check TTL
      if (parsed.ttl > 0 && Date.now() - parsed.timestamp > parsed.ttl) {
        localStorage.removeItem(key);
        return null;
      }

      return parsed.value;
    } catch {
      return null;
    }
  },

  removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch {
      // Ignore errors
    }
  },

  clear(prefix?: string): void {
    try {
      if (prefix) {
        const keys = Object.keys(localStorage).filter(key =>
          key.startsWith(prefix)
        );
        keys.forEach(key => localStorage.removeItem(key));
      } else {
        localStorage.clear();
      }
    } catch {
      // Ignore errors
    }
  }
};

/**
 * Debug utilities
 */
export const DebugUtils = {
  /**
   * Log API request/response for debugging
   */
  logApiCall(
    method: string,
    url: string,
    request?: any,
    response?: any,
    error?: any
  ): void {
    if (typeof console === "undefined" || !console.group) return;

    const timestamp = new Date().toISOString();
    console.group(`🌐 API Call [${method.toUpperCase()}] ${timestamp}`);
    console.log("URL:", url);

    if (request) {
      console.log("Request:", request);
    }

    if (response) {
      console.log("Response:", response);
    }

    if (error) {
      console.error("Error:", error);
    }

    console.groupEnd();
  },

  /**
   * Performance measurement
   */
  measurePerformance<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();

    return fn().finally(() => {
      const duration = performance.now() - start;
      console.log(`⏱️ ${name} took ${duration.toFixed(2)}ms`);
    });
  }
};
