import type { OptionsType } from "@/components/ReSegmented";

const menuTypeOptions: Array<OptionsType> = [
  {
    label: "Menu",
    value: 0
  },
  {
    label: "<PERSON>rame",
    value: 1
  },
  {
    label: "External Link",
    value: 2
  },
  {
    label: "Button",
    value: 3
  }
];

const showLinkOptions: Array<OptionsType> = [
  {
    label: "Show",
    tip: "Will be displayed in menu",
    value: true
  },
  {
    label: "Hide",
    tip: "Will not be displayed in menu",
    value: false
  }
];

const fixedTagOptions: Array<OptionsType> = [
  {
    label: "Fixed",
    tip: "Current menu name is fixed in tabs and cannot be closed",
    value: true
  },
  {
    label: "Not Fixed",
    tip: "Current menu name is not fixed in tabs and can be closed",
    value: false
  }
];

const keepAliveOptions: Array<OptionsType> = [
  {
    label: "Cache",
    tip: "Will save the page's overall state, state will be cleared after refresh",
    value: true
  },
  {
    label: "No Cache",
    tip: "Will not save the page's overall state",
    value: false
  }
];

const hiddenTagOptions: Array<OptionsType> = [
  {
    label: "Allow",
    tip: "Current menu name or custom info can be added to tabs",
    value: false
  },
  {
    label: "Forbid",
    tip: "Current menu name or custom info cannot be added to tabs",
    value: true
  }
];

const showParentOptions: Array<OptionsType> = [
  {
    label: "Show",
    tip: "Will show parent menu",
    value: true
  },
  {
    label: "Hide",
    tip: "Will not show parent menu",
    value: false
  }
];

const frameLoadingOptions: Array<OptionsType> = [
  {
    label: "Enable",
    tip: "Has first-time loading animation",
    value: true
  },
  {
    label: "Disable",
    tip: "No first-time loading animation",
    value: false
  }
];

export {
  menuTypeOptions,
  showLinkOptions,
  fixedTagOptions,
  keepAliveOptions,
  hiddenTagOptions,
  showParentOptions,
  frameLoadingOptions
};
