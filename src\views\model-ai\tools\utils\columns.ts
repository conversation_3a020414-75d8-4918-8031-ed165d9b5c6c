import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Tool Name")
  },
  {
    prop: "slug",
    align: "left",
    sortable: true,
    minWidth: 140,
    headerRenderer: () => $t("Slug")
  },
  {
    prop: "toolType",
    align: "center",
    width: 120,
    headerRenderer: () => $t("Tool Type"),
    cellRenderer: ({ row }) => {
      const typeColors = {
        function: "primary",
        plugin: "success",
        integration: "info",
        custom: "warning"
      };
      return h(
        ElTag,
        {
          type: typeColors[row.toolType] || "info",
          size: "small"
        },
        () => row.toolType?.toUpperCase()
      );
    }
  },
  {
    prop: "version",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Version"),
    formatter: ({ version }) => version || "-"
  },
  {
    prop: "categories",
    align: "left",
    width: 150,
    headerRenderer: () => $t("Categories"),
    cellRenderer: ({ row }) => {
      if (!row.categories || row.categories.length === 0) {
        return "-";
      }
      return h(
        "div",
        {
          class: "flex flex-wrap gap-1"
        },
        row.categories.map((cat: any) =>
          h(
            ElTag,
            {
              size: "small",
              type: "info"
            },
            () => cat.name
          )
        )
      );
    }
  },
  {
    prop: "isActive",
    align: "center",
    width: 80,
    headerRenderer: () => $t("Active"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.isActive ? "success" : "danger",
          size: "small"
        },
        () => (row.isActive ? $t("Yes") : $t("No"))
      );
    }
  },
  {
    prop: "status",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.status === "active" ? "success" : "danger",
          size: "small"
        },
        () => row.status?.toUpperCase()
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
