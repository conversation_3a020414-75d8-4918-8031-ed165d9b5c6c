import { reactive, ref } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, ProviderFilterProps } from "./type";
import {
  getProviders,
  getProvidersDropdown,
  createProvider,
  updateProviderById,
  bulkDeleteProviders,
  deleteProvider,
  deleteProviderPermanent,
  bulkDeleteProvidersPermanent,
  restoreProvider,
  bulkRestoreProviders
} from "./auth-api";

export function useProviderHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<ProviderFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const providersDropdown = ref([]);

  // Form refs
  const providerFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "active"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetProviders = async () => {
    try {
      loading.value = true;
      const res = await getProviders(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching providers:", error);
      message($t("Failed to fetch providers"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnGetProvidersDropdown = async () => {
    try {
      const res = await getProvidersDropdown();
      providersDropdown.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching providers dropdown:", error);
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = async () => {
    await fnGetProviders();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetProviders();
  };

  const fnHandleSortChange = async (val: Record<string, any>) => {
    sort.value = {
      sortBy: val.prop,
      sortOrder: val.order == "ascending" ? "asc" : "desc"
    };
    await fnGetProviders();
  };

  const fnHandleCreateProvider = async (data: any) => {
    try {
      loading.value = true;
      const response = await createProvider(data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetProviders();
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateProvider = async (id: number, data: any) => {
    try {
      loading.value = true;
      const response = await updateProviderById(id, data);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetProviders();
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteProvider(row.id);
      message($t("Deleted successfully"), { type: "success" });
      await fnGetProviders();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting provider:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteProviders({ ids: selectedIds });
      message($t("Deleted successfully"), { type: "success" });
      fnGetProviders();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting providers:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteProviderPermanent(row.id);
      message($t("Permanently deleted successfully"), { type: "success" });
      fnGetProviders();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting provider:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteProvidersPermanent({ ids: selectedIds });
      message($t("Permanently deleted successfully"), { type: "success" });
      fnGetProviders();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting providers:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await restoreProvider(row.id);
      message($t("Restored successfully"), { type: "success" });
      fnGetProviders();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error restoring provider:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  const handleBulkRestore = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await bulkRestoreProviders({ ids: selectedIds });
      message($t("Restored successfully"), { type: "success" });
      await fnGetProviders();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk restoring providers:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };
  /*
   ***************************
   *   Form handlers and actions
   ***************************
   */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...row };
    drawerVisible.value = true;
  };

  const handleFilter = async (values: ProviderFilterProps) => {
    filterRef.value = values;
    await fnGetProviders();
  };

  const handleSubmit = async (values: FieldValues) => {
    let success = false;
    if (values.id != null) {
      success = await fnHandleUpdateProvider(Number(values.id), values);
    } else {
      success = await fnHandleCreateProvider(values);
      if (success) {
        drawerValues.value = { status: "active" };
        providerFormRef.value?.resetForm();
      }
    }
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    providersDropdown,
    handleBulkDelete,
    handleDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,
    fnGetProviders,
    fnGetProvidersDropdown,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    providerFormRef,
    handleSubmit,
    handleFilter,
    handleEdit
  };
}
