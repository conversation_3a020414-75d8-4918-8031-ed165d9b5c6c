const Layout = () => import("@/layout/index.vue");

export default {
  path: "/auth/ai-knowledge/management",
  name: "KnowledgeBase",
  component: Layout,
  redirect: "/auth/ai-knowledge",
  meta: {
    icon: "ri:book-line",
    title: "AI Knowledge",
    rank: 6
  },
  children: [
    {
      path: "/auth/ai-knowledge",
      name: "KnowledgeBaseIndex",
      component: () => import("@/views/knowledge-base/index.vue"),
      meta: {
        icon: "ri:book-line",
        title: "AI Knowledge",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
