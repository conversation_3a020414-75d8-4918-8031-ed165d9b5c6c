export type Provider = {
  id: number;
  key: string;
  name: string;
  description?: string;
  baseUrl?: string;
  apiKey?: string;
  credentials?: string;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type FormItemProps = {
  id?: number | null;
  key?: string;
  name?: string;
  description?: string;
  baseUrl?: string;
  apiKey?: string;
  credentials?: string;
  status?: "active" | "inactive";
  [key: string]: any;
};

export type ProviderFilterProps = {
  key?: string;
  name?: string;
  status?: "active" | "inactive";
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};

export type ProviderDropdownItem = {
  id: number;
  key: string;
  name: string;
};
