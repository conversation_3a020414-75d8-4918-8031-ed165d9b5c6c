import { $t } from "@/plugins/i18n";
import type { FormItemRule } from "element-plus";

const SPECIAL_CHARS = /[!@#$%^&*(),.?":{}|<>]/;
const HAS_NUMBER = /\d/;
const HAS_UPPERCASE = /[A-Z]/;
const HAS_LOWERCASE = /[a-z]/;

export interface PasswordRules {
  minLength?: number;
  requireNumbers?: boolean;
  requireSymbols?: boolean;
  requireLowercase?: boolean;
  requireUppercase?: boolean;
}

export const passwordRule = (options: PasswordRules = {}): FormItemRule => {
  const {
    minLength = 6,
    requireNumbers = true,
    requireSymbols = true,
    requireLowercase = true,
    requireUppercase = true
  } = options;

  return {
    validator: (rule: any, value: string, callback: Function) => {
      if (!value) {
        callback(new Error($t("Password required")));
        return;
      }

      if (value.length < minLength) {
        callback(
          new Error(
            $t("Min. :minLength characters", {
              minLength: minLength
            })
          )
        );
        return;
      }

      if (requireNumbers && !HAS_NUMBER.test(value)) {
        callback(new Error($t("Password required numbers")));
        return;
      }

      if (requireSymbols && !SPECIAL_CHARS.test(value)) {
        callback(new Error($t("Password required symbols")));
        return;
      }

      if (requireUppercase && !HAS_UPPERCASE.test(value)) {
        callback(new Error($t("Password required uppercase")));
        return;
      }

      if (requireLowercase && !HAS_LOWERCASE.test(value)) {
        callback(new Error($t("Password required lowercase")));
        return;
      }

      callback();
    },
    trigger: "blur"
  };
};
